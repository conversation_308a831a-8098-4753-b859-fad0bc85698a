# Agent Template System Documentation

## Overview

The Agent Template System is a comprehensive solution for managing, sharing, and reusing agent configurations in the Metamorphic Reactor. It provides a robust foundation for creating agents from pre-defined templates with advanced features like inheritance, composition, versioning, and community ratings.

## Key Features

### 1. Template Storage and Management
- **Comprehensive Metadata**: Each template includes detailed metadata with author, version, description, category, tags, and more
- **Status Management**: Templates can be in draft, active, deprecated, or archived states
- **Categorization**: Templates are organized into categories like role-specific, domain-specific, general-purpose, etc.
- **Tagging System**: Flexible tagging system for better organization and discoverability

### 2. CRUD Operations
- **Create**: Create new templates from scratch or from existing agent profiles
- **Read**: Retrieve templates by ID, name, or search criteria
- **Update**: Modify existing templates with version tracking
- **Delete**: Soft delete (archive) or hard delete templates

### 3. Template Categories and Organization
```python
class TemplateCategory(str, Enum):
    ROLE_SPECIFIC = "role_specific"          # Templates for specific agent roles
    TASK_SPECIFIC = "task_specific"          # Templates for specific types of tasks
    DOMAIN_SPECIFIC = "domain_specific"      # Templates for specific domains (e.g., Python, web dev)
    GENERAL_PURPOSE = "general_purpose"      # General-purpose templates
    SPECIALIZED = "specialized"              # Highly specialized templates
    COMMUNITY = "community"                  # Community-contributed templates
    CUSTOM = "custom"                        # User-created custom templates
```

### 4. Template Versioning and History
- **Version Control**: Semantic versioning (major.minor.patch)
- **Change Tracking**: Detailed change logs for each version
- **Breaking Changes**: Special handling for breaking changes
- **Version History**: Complete history of all template versions

### 5. Search and Filtering
- **Multi-criteria Search**: Search by query, role, category, tags, author, rating, and status
- **Popularity Ranking**: Sort by usage count and ratings
- **Recommendation System**: Get recommended templates based on task descriptions
- **Tag-based Discovery**: Find templates by tags

### 6. Template Inheritance and Composition
- **Inheritance**: Create child templates that inherit from parent templates
- **Composition**: Combine multiple templates into a single template
- **Variable Substitution**: Support for template variables and dynamic content
- **Conditional Logic**: Support for conditional template behavior

### 7. Quality Assurance and Validation
- **Multi-level Validation**: Basic, comprehensive, and strict validation levels
- **Capability Validation**: Ensure capabilities match agent roles
- **Model Compatibility**: Validate model-role compatibility
- **Circular Dependency Detection**: Prevent circular dependencies in composition

### 8. Community Features
- **Rating System**: 5-star rating system with comments
- **Helpful Votes**: Vote on rating helpfulness
- **Usage Statistics**: Track template usage and success rates
- **Public/Private Templates**: Control template visibility

## Core Classes

### AgentTemplate
The main template class with the following properties:
- `id`: Unique template identifier
- `name`: Template name
- `metadata`: Template metadata (author, version, description, etc.)
- `role`: Agent role this template is designed for
- `base_prompt`: Base prompt template
- `capabilities`: List of agent capabilities
- `preferred_models`: Preferred AI models
- `model_config`: Model configuration settings
- `variables`: Template variables for customization
- `composition`: List of other templates to compose
- `validation_rules`: Validation rules for the template

### TemplateManager
The central management class providing:
- Template CRUD operations
- Search and filtering functionality
- Validation and quality checks
- Version management
- Import/export capabilities
- Usage tracking and statistics

### TemplateMetadata
Comprehensive metadata including:
- Author information
- Version and compatibility data
- Category and tags
- Creation and update timestamps
- License information
- Parent/child relationships

## Usage Examples

### Creating a Template
```python
# Create a template from an existing agent
template = factory.create_agent_template(
    agent_id="agent_123",
    template_name="Python Expert",
    author="user123",
    description="Expert Python developer template",
    category=TemplateCategory.DOMAIN_SPECIFIC,
    tags={"python", "development", "expert"},
    is_public=True
)

# Create a template from scratch
template = factory.template_manager.create_template(
    name="Code Reviewer",
    role=AgentRole.CRITIC,
    base_prompt="You are an expert code reviewer...",
    author="system",
    capabilities=["code_review", "security_analysis"],
    category=TemplateCategory.ROLE_SPECIFIC,
    tags={"review", "quality", "security"}
)
```

### Searching Templates
```python
# Search by query
templates = factory.search_templates(query="python")

# Search by role and category
templates = factory.search_templates(
    role=AgentRole.CODER,
    category=TemplateCategory.DOMAIN_SPECIFIC
)

# Get popular templates
popular = factory.get_popular_templates(limit=10)

# Get recommended templates for a task
recommendations = factory.get_recommended_templates_for_task(
    "I need to review Python code for security issues"
)
```

### Creating Agents from Templates
```python
# Create agent from template
agent = factory.create_agent_from_template(
    template="template_id_123",
    name="Python Reviewer",
    variables={"domain": "web development"}
)

# Create a team from template
team = factory.create_agent_team_from_template(
    template_id="template_id_123",
    team_name="Review Team",
    team_size=3
)
```

### Rating and Community Features
```python
# Rate a template
factory.rate_template("template_id", "user123", 5, "Excellent template!")

# Get template statistics
stats = factory.get_template_statistics()

# Export/Import templates
exported = factory.export_template("template_id")
imported = factory.import_template(exported, author="new_user")
```

## Template Variables and Customization

Templates support variables for dynamic content:
```python
template = factory.template_manager.create_template(
    name="Custom Assistant",
    base_prompt="You are a {role} specializing in {domain}. Your expertise level is {level}.",
    variables={
        "role": {"type": "string", "default": "assistant", "required": True},
        "domain": {"type": "string", "default": "general", "required": True},
        "level": {"type": "string", "choices": ["beginner", "intermediate", "expert"], "default": "intermediate"}
    }
)

# Use template with variables
agent = factory.create_agent_from_template(
    template,
    "Domain Expert",
    variables={"role": "consultant", "domain": "machine learning", "level": "expert"}
)
```

## Integration with AgentFactory

The template system is fully integrated with the AgentFactory:
- Templates are automatically initialized with default system templates
- Agent creation is enhanced with template support
- Template usage is tracked automatically
- Validation ensures template compatibility

## Default Templates

The system includes several default templates:
1. **Code Review Specialist** - For comprehensive code review tasks
2. **Task Planner** - For project planning and task breakdown
3. **Python Developer** - For Python development tasks

## Best Practices

1. **Use Descriptive Names**: Template names should clearly indicate their purpose
2. **Comprehensive Descriptions**: Include detailed descriptions for better discoverability
3. **Proper Categorization**: Use appropriate categories and tags
4. **Version Management**: Use semantic versioning and detailed change logs
5. **Validation**: Always validate templates before publishing
6. **Community Engagement**: Rate and review templates to help others

## Future Enhancements

The template system is designed to be extensible and can be enhanced with:
- Advanced NLP-based template matching
- Machine learning-powered recommendations
- Template marketplace integration
- Advanced analytics and insights
- Automated template optimization
- Integration with external template repositories

## API Reference

### AgentFactory Template Methods
- `create_agent_template()` - Create template from agent
- `create_agent_from_template()` - Create agent from template
- `search_templates()` - Search templates
- `get_template()` - Get template by ID
- `rate_template()` - Rate a template
- `export_template()` - Export template
- `import_template()` - Import template

### TemplateManager Methods
- `create_template()` - Create new template
- `update_template()` - Update existing template
- `delete_template()` - Delete template
- `validate_template()` - Validate template
- `search_templates()` - Search with filters
- `get_template_statistics()` - Get system statistics

This comprehensive template system provides a solid foundation for agent reusability, community sharing, and continuous improvement of agent configurations.