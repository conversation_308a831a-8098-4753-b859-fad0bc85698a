import * as vscode from 'vscode';
import { Worker } from 'worker_threads';
import * as path from 'path';
import { MessageProtocol, WorkerMessage, WorkerResponse, TaskPayload, TaskResult } from './messageProtocol';
import { ErrorRecoveryManager } from './errorRecovery';
import { PerformanceMonitor } from './performanceMonitor';
import { serviceRegistry } from '../utils/serviceRegistry';

export interface AgentWorker {
  id: string;
  worker: Worker;
  status: 'idle' | 'running' | 'error' | 'stopped';
  role: 'planner' | 'critic' | 'additional';
  lastActivity: number;
  pendingMessages: Map<string, { resolve: Function; reject: Function; timeout: NodeJS.Timeout }>;
}

export class WorkerThreadManager implements vscode.Disposable {
  private workers: Map<string, AgentWorker> = new Map();
  private maxWorkers: number = 2; // Default to free tier
  private staggerDelay: number = 200; // 200ms delay between spawns
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private currentTask: string | null = null;
  private taskResults: Map<string, TaskResult[]> = new Map();
  private errorRecovery: ErrorRecoveryManager;
  private performanceMonitor: PerformanceMonitor;
  private isDisposed: boolean = false;

  constructor(private context: vscode.ExtensionContext) {
    this.initializeMaxWorkers();
    this.errorRecovery = new ErrorRecoveryManager(this);
    this.performanceMonitor = new PerformanceMonitor();
    this.startHealthChecks();
  }

  private initializeMaxWorkers(): void {
    const config = vscode.workspace.getConfiguration('metamorphic-reactor');
    this.maxWorkers = config.get<number>('maxAgents', 2);
  }

  public updateMaxWorkers(maxAgents: number): void {
    this.maxWorkers = maxAgents;
    console.log(`Updated max workers to: ${maxAgents}`);
  }

  public async spawnAgents(taskId: string): Promise<string[]> {
    const agentIds: string[] = [];
    
    try {
      // Always spawn at least 2 agents (planner + critic)
      const roles: Array<'planner' | 'critic' | 'additional'> = ['planner', 'critic'];
      
      // Add additional agents based on subscription tier
      for (let i = 2; i < this.maxWorkers; i++) {
        roles.push('additional');
      }

      for (let i = 0; i < roles.length; i++) {
        const agentId = `${taskId}-agent-${i}`;
        const role = roles[i];
        
        // Stagger agent spawning to prevent UI jank
        if (i > 0) {
          await this.delay(this.staggerDelay);
        }
        
        await this.spawnWorker(agentId, role);
        agentIds.push(agentId);
      }

      return agentIds;
    } catch (error) {
      console.error('Error spawning agents:', error);
      throw new Error(`Failed to spawn agents: ${error}`);
    }
  }

  private async spawnWorker(agentId: string, role: 'planner' | 'critic' | 'additional'): Promise<void> {
    try {
      // Get compiled worker script path
      const workerScript = path.join(this.context.extensionPath, 'out', 'workers', 'agentWorker.js');
      
      const worker = new Worker(workerScript, {
        workerData: {
          agentId,
          role,
          contextPath: this.context.extensionPath
        }
      });

      const agentWorker: AgentWorker = {
        id: agentId,
        worker,
        status: 'idle',
        role,
        lastActivity: Date.now(),
        pendingMessages: new Map()
      };

      // Set up worker event handlers
      worker.on('message', (response: WorkerResponse) => this.handleWorkerMessage(agentId, response));
      worker.on('error', (error) => this.handleWorkerError(agentId, error));
      worker.on('exit', (code) => this.handleWorkerExit(agentId, code));

      this.workers.set(agentId, agentWorker);
      console.log(`Agent ${agentId} (${role}) spawned successfully`);
      
      // Initialize performance monitoring for this agent
      this.performanceMonitor.initializeAgent(agentId, role);
      
      // Send initial ping to verify worker is responsive
      await this.sendMessageToWorker(agentId, MessageProtocol.createPingMessage(agentId));
      
    } catch (error) {
      console.error(`Failed to spawn worker ${agentId}:`, error);
      throw error;
    }
  }

  private handleWorkerMessage(agentId: string, response: WorkerResponse): void {
    const agentWorker = this.workers.get(agentId);
    if (!agentWorker) {
      console.warn(`Received message from unknown agent: ${agentId}`);
      return;
    }

    // Update last activity
    agentWorker.lastActivity = Date.now();

    // Record performance metrics
    this.performanceMonitor.recordMessageReceived(agentId, response.messageId);

    // Handle pending message responses
    if (response.messageId) {
      const pending = agentWorker.pendingMessages.get(response.messageId);
      if (pending) {
        clearTimeout(pending.timeout);
        agentWorker.pendingMessages.delete(response.messageId);
        pending.resolve(response);
      }
    }

    // Handle different response types
    switch (response.type) {
      case 'status':
        this.handleStatusUpdate(agentId, response.payload);
        break;
      case 'result':
        this.handleTaskResult(agentId, response.payload);
        break;
      case 'token':
        this.handleTokenStream(agentId, response.payload);
        break;
      case 'error':
        this.handleWorkerError(agentId, new Error(response.payload));
        break;
      case 'pong':
        console.log(`Agent ${agentId} is responsive`);
        break;
      default:
        console.warn(`Unknown response type from agent ${agentId}:`, response.type);
    }
  }

  private handleStatusUpdate(agentId: string, statusPayload: any): void {
    const agentWorker = this.workers.get(agentId);
    if (agentWorker && statusPayload.status) {
      agentWorker.status = statusPayload.status;
      console.log(`Agent ${agentId} status: ${statusPayload.status}`);
      
      // Notify webview of status change
      this.notifyWebView('updateAgentStatus', this.getWorkerStatus());
    }
  }

  private handleTaskResult(agentId: string, result: any): void {
    if (!this.currentTask) return;

    const agentWorker = this.workers.get(agentId);
    if (!agentWorker) return;

    // Store task result
    if (!this.taskResults.has(this.currentTask)) {
      this.taskResults.set(this.currentTask, []);
    }

    const taskResults = this.taskResults.get(this.currentTask)!;
    taskResults.push({
      agentId,
      role: agentWorker.role,
      response: result.response,
      consensus: result.consensus,
      timestamp: result.timestamp,
      tokenCount: result.tokenCount
    });

    // Check if task is complete
    if (MessageProtocol.isTaskComplete(taskResults)) {
      this.handleTaskComplete(this.currentTask, taskResults);
    } else {
      // Check for meta-block condition
      const consensusScore = MessageProtocol.calculateConsensusScore(taskResults);
      if (consensusScore < 1.0) {
        console.log(`Partial consensus (${Math.round(consensusScore * 100)}%) - continuing...`);
      }
    }
  }

  private handleTokenStream(agentId: string, token: string): void {
    // Forward token stream to webview
    this.notifyWebView('tokenStream', { agentId, token });
  }

  private handleTaskComplete(taskId: string, results: TaskResult[]): void {
    console.log(`Task ${taskId} completed with consensus:`, results);
    
    // Notify webview
    this.notifyWebView('taskComplete', {
      taskId,
      results,
      consensusScore: MessageProtocol.calculateConsensusScore(results)
    });

    // Clean up
    this.currentTask = null;
    this.taskResults.delete(taskId);
  }

  private async handleWorkerError(agentId: string, error: Error): Promise<void> {
    console.error(`Error in agent ${agentId}:`, error);
    
    // Record error in performance metrics
    this.performanceMonitor.recordError(agentId);
    
    const worker = this.workers.get(agentId);
    if (worker) {
      worker.status = 'error';
    }

    // Attempt error recovery
    try {
      const recovered = await this.errorRecovery.handleError(error, agentId);
      if (recovered) {
        console.log(`Successfully recovered from error in agent ${agentId}`);
      } else {
        console.warn(`Failed to recover from error in agent ${agentId}`);
      }
    } catch (recoveryError) {
      console.error(`Error during recovery for agent ${agentId}:`, recoveryError);
    }

    // Notify webview of status change
    this.notifyWebView('updateAgentStatus', this.getWorkerStatus());
  }

  private handleWorkerExit(agentId: string, code: number): void {
    console.log(`Agent ${agentId} exited with code ${code}`);
    const worker = this.workers.get(agentId);
    if (worker) {
      worker.status = 'stopped';
    }
  }

  public async terminateWorkers(): Promise<void> {
    const terminationPromises: Promise<number>[] = [];
    
    for (const [agentId, agentWorker] of this.workers) {
      console.log(`Terminating agent ${agentId}`);
      terminationPromises.push(agentWorker.worker.terminate());
    }

    try {
      await Promise.all(terminationPromises);
      this.workers.clear();
      console.log('All worker threads terminated successfully');
    } catch (error) {
      console.error('Error terminating worker threads:', error);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getWorkerStatus(): Array<{ id: string; status: string; role: string }> {
    return Array.from(this.workers.values()).map(worker => ({
      id: worker.id,
      status: worker.status,
      role: worker.role
    }));
  }

  // Add new methods for thread-safe communication

  public async sendMessageToWorker(agentId: string, message: WorkerMessage, timeoutMs: number = 5000): Promise<WorkerResponse> {
    if (this.isDisposed) {
      throw new Error('WorkerThreadManager has been disposed');
    }

    const agentWorker = this.workers.get(agentId);
    if (!agentWorker) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // Record performance metrics
    this.performanceMonitor.recordMessageSent(agentId, message.messageId);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        agentWorker.pendingMessages.delete(message.messageId);
        
        // Record timeout in performance metrics
        this.performanceMonitor.recordTimeout(agentId);
        
        reject(new Error(`Message timeout for agent ${agentId}`));
      }, timeoutMs);

      agentWorker.pendingMessages.set(message.messageId, { resolve, reject, timeout });
      agentWorker.worker.postMessage(message);
    });
  }

  public async sendTaskToAgents(taskPayload: TaskPayload): Promise<void> {
    const taskId = `task-${Date.now()}`;
    this.currentTask = taskId;

    const promises: Promise<void>[] = [];

    for (const [agentId, agentWorker] of this.workers) {
      if (agentWorker.status === 'idle') {
        const taskMessage = MessageProtocol.createTaskMessage(taskPayload, agentId);
        
        promises.push(
          this.sendMessageToWorker(agentId, taskMessage)
            .then(() => {
              console.log(`Task sent to agent ${agentId}`);
            })
            .catch(error => {
              console.error(`Failed to send task to agent ${agentId}:`, error);
            })
        );
      }
    }

    try {
      await Promise.all(promises);
      console.log(`Task ${taskId} sent to all available agents`);
    } catch (error) {
      console.error('Failed to send task to some agents:', error);
    }
  }

  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  private async performHealthCheck(): Promise<void> {
    const currentTime = Date.now();
    const healthPromises: Promise<void>[] = [];

    for (const [agentId, agentWorker] of this.workers) {
      // Check if agent has been inactive for too long
      if (currentTime - agentWorker.lastActivity > 60000) { // 1 minute
        console.warn(`Agent ${agentId} appears inactive, performing health check`);
        
        const pingMessage = MessageProtocol.createPingMessage(agentId);
        healthPromises.push(
          this.sendMessageToWorker(agentId, pingMessage, 3000)
            .then(() => {
              console.log(`Agent ${agentId} health check passed`);
            })
            .catch(error => {
              console.error(`Agent ${agentId} health check failed:`, error);
              agentWorker.status = 'error';
              this.notifyWebView('updateAgentStatus', this.getWorkerStatus());
            })
        );
      }
    }

    await Promise.allSettled(healthPromises);
  }

  private notifyWebView(command: string, payload: any): void {
    // Get webview provider from service registry and send message
    const webViewProvider = serviceRegistry.getWebViewProvider();
    if (webViewProvider) {
      webViewProvider.sendMessage({ command, ...payload });
    }
  }

  // Performance and monitoring methods
  public getPerformanceReport(): any {
    return this.performanceMonitor.getPerformanceReport();
  }

  public getErrorRecoveryReport(): any {
    return this.errorRecovery.getRecoveryReport();
  }

  public async gracefulShutdown(): Promise<void> {
    console.log('Initiating graceful shutdown of worker threads...');
    
    this.isDisposed = true;

    // Stop accepting new tasks
    this.currentTask = null;

    // Stop health checks
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Send stop messages to all workers
    const stopPromises: Promise<void>[] = [];
    for (const [agentId, agentWorker] of this.workers) {
      if (agentWorker.status !== 'stopped') {
        const stopMessage = MessageProtocol.createStopMessage(agentId);
        stopPromises.push(
          this.sendMessageToWorker(agentId, stopMessage, 3000)
            .then(() => {
              console.log(`Agent ${agentId} stopped gracefully`);
            })
            .catch(error => {
              console.warn(`Failed to stop agent ${agentId} gracefully:`, error);
            })
        );
      }
    }

    // Wait for all agents to stop gracefully (with timeout)
    await Promise.race([
      Promise.allSettled(stopPromises),
      new Promise(resolve => setTimeout(resolve, 5000)) // 5 second timeout
    ]);

    // Force terminate any remaining workers
    await this.terminateWorkers();

    console.log('Graceful shutdown completed');
  }

  public dispose(): void {
    console.log('Disposing WorkerThreadManager...');

    // Mark as disposed
    this.isDisposed = true;

    // Stop health checks
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Clean up all pending messages
    for (const agentWorker of this.workers.values()) {
      for (const pending of agentWorker.pendingMessages.values()) {
        clearTimeout(pending.timeout);
        pending.reject(new Error('Worker thread manager disposed'));
      }
      agentWorker.pendingMessages.clear();
    }

    // Clean up performance monitoring
    this.performanceMonitor.dispose();

    // Clean up error recovery
    this.errorRecovery.dispose();

    // Clear task results
    this.taskResults.clear();

    // Terminate workers
    this.terminateWorkers().catch(error => {
      console.error('Error during worker cleanup:', error);
    });

    console.log('WorkerThreadManager disposed successfully');
  }
}