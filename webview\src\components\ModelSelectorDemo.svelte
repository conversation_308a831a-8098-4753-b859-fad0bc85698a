<script lang="ts">
  import ModelSelector from './ModelSelector.svelte';
  
  let selectedModel1 = 'gpt-4';
  let selectedModel2 = 'claude-3-sonnet';
  let selectedModel3 = 'gpt-4-turbo';
  let selectedModel4 = 'claude-3-opus';
  
  function handleModelSelected(event) {
    console.log('Model selected:', event.detail);
  }
</script>

<div class="max-w-6xl mx-auto p-6 space-y-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-white mb-2">Model Selector Component</h1>
    <p class="text-gray-400">Flexible model selection with multiple layouts and configurations</p>
  </div>
  
  <!-- Dropdown Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Dropdown Layout</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Default Dropdown</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel1}
          layout="dropdown"
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Compact Dropdown</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel1}
          layout="dropdown"
          size="small"
          showDescription={false}
          on:modelSelected={handleModelSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Grid Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Grid Layout</h2>
    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Full Grid</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel2}
          layout="grid"
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">OpenAI Models Only</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel2}
          layout="grid"
          filterByProvider="OpenAI"
          on:modelSelected={handleModelSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- List Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">List Layout</h2>
    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Full List</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel3}
          layout="list"
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Anthropic Models Only</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel3}
          layout="list"
          filterByProvider="Anthropic"
          on:modelSelected={handleModelSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Size Variations -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Size Variations</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Small</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel4}
          layout="dropdown"
          size="small"
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Medium (Default)</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel4}
          layout="dropdown"
          size="medium"
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Large</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel4}
          layout="dropdown"
          size="large"
          on:modelSelected={handleModelSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Configuration Options -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Configuration Options</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Minimal (No Descriptions)</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel1}
          layout="dropdown"
          showDescription={false}
          showProvider={false}
          on:modelSelected={handleModelSelected}
        />
      </div>
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Disabled State</h3>
        <ModelSelector 
          bind:selectedModel={selectedModel1}
          layout="dropdown"
          disabled={true}
          on:modelSelected={handleModelSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Current Selections -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Current Selections</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-400">Dropdown:</span>
          <span class="text-white font-medium">{selectedModel1}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">Grid:</span>
          <span class="text-white font-medium">{selectedModel2}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">List:</span>
          <span class="text-white font-medium">{selectedModel3}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">Size Demo:</span>
          <span class="text-white font-medium">{selectedModel4}</span>
        </div>
      </div>
      <div class="text-sm text-gray-400">
        <p>The ModelSelector component supports:</p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>Multiple layout options (dropdown, grid, list)</li>
          <li>Provider filtering (OpenAI, Anthropic, Google)</li>
          <li>Size variations (small, medium, large)</li>
          <li>Configurable descriptions and provider info</li>
          <li>Disabled state support</li>
          <li>Model capabilities and pricing info</li>
          <li>Recommended model indicators</li>
          <li>Responsive design</li>
        </ul>
      </div>
    </div>
  </section>
</div>