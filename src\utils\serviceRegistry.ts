/**
 * Service Registry for Metamorphic Reactor
 * Manages cross-service communication without modifying the extension context
 */

import { CommandManager } from '../commands/commandManager';
import { WorkerThreadManager } from '../services/workerThreadManager';
import { WebViewProvider } from '../providers/webViewProvider';
import { SettingsManager } from './settingsManager';

class ServiceRegistry {
  private static instance: ServiceRegistry;
  
  private services: Map<string, any> = new Map();

  private constructor() {}

  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  public register<T>(name: string, service: T): void {
    this.services.set(name, service);
  }

  public get<T>(name: string): T | undefined {
    return this.services.get(name) as T;
  }

  public has(name: string): boolean {
    return this.services.has(name);
  }

  public clear(): void {
    this.services.clear();
  }

  // Typed getters for known services
  public getWebViewProvider(): WebViewProvider | undefined {
    return this.get<WebViewProvider>('webViewProvider');
  }

  public getWorkerThreadManager(): WorkerThreadManager | undefined {
    return this.get<WorkerThreadManager>('workerThreadManager');
  }

  public getSettingsManager(): SettingsManager | undefined {
    return this.get<SettingsManager>('settingsManager');
  }

  public getCommandManager(): CommandManager | undefined {
    return this.get<CommandManager>('commandManager');
  }
}

export const serviceRegistry = ServiceRegistry.getInstance();