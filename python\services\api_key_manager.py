"""
API Key Management System for Metamorphic Reactor
Manages API keys for multiple LLM providers with rotation and pooling
"""

import asyncio
import logging
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import aiosqlite
from cryptography.fernet import Fernet
import base64
import os

logger = logging.getLogger(__name__)

class LLMProvider(str, Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"

class KeyStatus(str, Enum):
    """API key status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    RATE_LIMITED = "rate_limited"
    QUOTA_EXCEEDED = "quota_exceeded"
    INVALID = "invalid"
    SUSPENDED = "suspended"

@dataclass
class APIKeyInfo:
    """API key information and metadata"""
    key_id: str
    provider: LLMProvider
    encrypted_key: str
    status: KeyStatus
    usage_count: int = 0
    last_used: Optional[datetime] = None
    rate_limit_reset: Optional[datetime] = None
    daily_quota: Optional[int] = None
    daily_usage: int = 0
    monthly_quota: Optional[int] = None
    monthly_usage: int = 0
    cost_limit_usd: Optional[float] = None
    cost_used_usd: float = 0.0
    priority: int = 1  # 1 = highest priority
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}

@dataclass
class UsageRecord:
    """API usage record"""
    record_id: str
    key_id: str
    provider: LLMProvider
    model_name: str
    tokens_used: int
    cost_usd: float
    request_timestamp: datetime
    response_time_ms: int
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

class APIKeyManager:
    """
    Manages API keys with encryption, rotation, and usage tracking
    
    Features:
    - Secure key storage with encryption
    - Automatic key rotation and load balancing
    - Rate limiting and quota management
    - Usage tracking and cost monitoring
    - Provider-specific configurations
    """
    
    def __init__(self, db_path: str = "data/api_keys.db", encryption_key: Optional[str] = None):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize encryption
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            # Generate or load encryption key
            key_file = self.db_path.parent / "encryption.key"
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    self.cipher = Fernet(f.read())
            else:
                key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(key)
                self.cipher = Fernet(key)
                logger.info("Generated new encryption key for API key storage")
        
        self.key_pools: Dict[LLMProvider, List[APIKeyInfo]] = {
            provider: [] for provider in LLMProvider
        }
        self.rotation_index: Dict[LLMProvider, int] = {
            provider: 0 for provider in LLMProvider
        }
        self._initialized = False
        
        # Provider-specific configurations
        self.provider_configs = {
            LLMProvider.OPENAI: {
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                "default_model": "gpt-3.5-turbo",
                "rate_limit_rpm": 3500,  # requests per minute
                "rate_limit_tpm": 90000,  # tokens per minute
                "cost_per_1k_tokens": {
                    "gpt-3.5-turbo": 0.002,
                    "gpt-4": 0.03,
                    "gpt-4-turbo": 0.01
                }
            },
            LLMProvider.ANTHROPIC: {
                "models": ["claude-3-haiku", "claude-3-sonnet", "claude-3-opus"],
                "default_model": "claude-3-haiku",
                "rate_limit_rpm": 1000,
                "rate_limit_tpm": 100000,
                "cost_per_1k_tokens": {
                    "claude-3-haiku": 0.00025,
                    "claude-3-sonnet": 0.003,
                    "claude-3-opus": 0.015
                }
            },
            LLMProvider.GOOGLE: {
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": "gemini-pro",
                "rate_limit_rpm": 60,
                "rate_limit_tpm": 32000,
                "cost_per_1k_tokens": {
                    "gemini-pro": 0.0005,
                    "gemini-pro-vision": 0.0025
                }
            }
        }
    
    async def initialize(self):
        """Initialize the API key manager"""
        if self._initialized:
            return
        
        await self._create_tables()
        await self._load_keys_from_db()
        
        self._initialized = True
        logger.info("API Key Manager initialized successfully")
    
    async def _create_tables(self):
        """Create database tables"""
        async with aiosqlite.connect(self.db_path) as db:
            # API keys table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    key_id TEXT PRIMARY KEY,
                    provider TEXT NOT NULL,
                    encrypted_key TEXT NOT NULL,
                    status TEXT NOT NULL,
                    usage_count INTEGER DEFAULT 0,
                    last_used TEXT,
                    rate_limit_reset TEXT,
                    daily_quota INTEGER,
                    daily_usage INTEGER DEFAULT 0,
                    monthly_quota INTEGER,
                    monthly_usage INTEGER DEFAULT 0,
                    cost_limit_usd REAL,
                    cost_used_usd REAL DEFAULT 0.0,
                    priority INTEGER DEFAULT 1,
                    metadata TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Usage records table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS usage_records (
                    record_id TEXT PRIMARY KEY,
                    key_id TEXT NOT NULL,
                    provider TEXT NOT NULL,
                    model_name TEXT NOT NULL,
                    tokens_used INTEGER NOT NULL,
                    cost_usd REAL NOT NULL,
                    request_timestamp TEXT NOT NULL,
                    response_time_ms INTEGER NOT NULL,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    metadata TEXT,
                    FOREIGN KEY (key_id) REFERENCES api_keys (key_id)
                )
            """)
            
            # Indexes
            await db.execute("CREATE INDEX IF NOT EXISTS idx_keys_provider ON api_keys (provider)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_keys_status ON api_keys (status)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_usage_timestamp ON usage_records (request_timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_usage_key_id ON usage_records (key_id)")
            
            await db.commit()
    
    async def _load_keys_from_db(self):
        """Load API keys from database into memory pools"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("SELECT * FROM api_keys") as cursor:
                async for row in cursor:
                    key_info = APIKeyInfo(
                        key_id=row[0],
                        provider=LLMProvider(row[1]),
                        encrypted_key=row[2],
                        status=KeyStatus(row[3]),
                        usage_count=row[4],
                        last_used=datetime.fromisoformat(row[5]) if row[5] else None,
                        rate_limit_reset=datetime.fromisoformat(row[6]) if row[6] else None,
                        daily_quota=row[7],
                        daily_usage=row[8],
                        monthly_quota=row[9],
                        monthly_usage=row[10],
                        cost_limit_usd=row[11],
                        cost_used_usd=row[12],
                        priority=row[13],
                        metadata=json.loads(row[14]) if row[14] else {},
                        created_at=datetime.fromisoformat(row[15]),
                        updated_at=datetime.fromisoformat(row[16])
                    )
                    
                    if key_info.status == KeyStatus.ACTIVE:
                        self.key_pools[key_info.provider].append(key_info)
        
        # Sort pools by priority
        for provider in LLMProvider:
            self.key_pools[provider].sort(key=lambda k: k.priority)
        
        total_keys = sum(len(pool) for pool in self.key_pools.values())
        logger.info(f"Loaded {total_keys} API keys from database")
    
    async def add_api_key(
        self,
        provider: LLMProvider,
        api_key: str,
        daily_quota: Optional[int] = None,
        monthly_quota: Optional[int] = None,
        cost_limit_usd: Optional[float] = None,
        priority: int = 1,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Add a new API key to the system
        
        Args:
            provider: LLM provider
            api_key: The actual API key
            daily_quota: Daily usage quota
            monthly_quota: Monthly usage quota
            cost_limit_usd: Cost limit in USD
            priority: Priority level (1 = highest)
            metadata: Additional metadata
            
        Returns:
            str: Generated key ID
        """
        await self.initialize()
        
        # Generate key ID
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()[:16]
        key_id = f"{provider.value}_{key_hash}"
        
        # Encrypt the API key
        encrypted_key = self.cipher.encrypt(api_key.encode()).decode()
        
        # Create key info
        key_info = APIKeyInfo(
            key_id=key_id,
            provider=provider,
            encrypted_key=encrypted_key,
            status=KeyStatus.ACTIVE,
            daily_quota=daily_quota,
            monthly_quota=monthly_quota,
            cost_limit_usd=cost_limit_usd,
            priority=priority,
            metadata=metadata or {}
        )
        
        # Save to database
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO api_keys (
                    key_id, provider, encrypted_key, status, usage_count,
                    last_used, rate_limit_reset, daily_quota, daily_usage,
                    monthly_quota, monthly_usage, cost_limit_usd, cost_used_usd,
                    priority, metadata, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                key_info.key_id, key_info.provider.value, key_info.encrypted_key,
                key_info.status.value, key_info.usage_count,
                key_info.last_used.isoformat() if key_info.last_used else None,
                key_info.rate_limit_reset.isoformat() if key_info.rate_limit_reset else None,
                key_info.daily_quota, key_info.daily_usage,
                key_info.monthly_quota, key_info.monthly_usage,
                key_info.cost_limit_usd, key_info.cost_used_usd,
                key_info.priority, json.dumps(key_info.metadata),
                key_info.created_at.isoformat(), key_info.updated_at.isoformat()
            ))
            await db.commit()
        
        # Add to memory pool
        self.key_pools[provider].append(key_info)
        self.key_pools[provider].sort(key=lambda k: k.priority)
        
        logger.info(f"Added API key for {provider.value}: {key_id}")
        return key_id
    
    async def get_api_key(self, provider: LLMProvider, model: Optional[str] = None) -> Tuple[Optional[str], Optional[str]]:
        """
        Get the next available API key using round-robin rotation
        
        Args:
            provider: LLM provider
            model: Specific model (optional)
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (decrypted_api_key, key_id)
        """
        await self.initialize()
        
        pool = self.key_pools[provider]
        if not pool:
            logger.warning(f"No API keys available for provider: {provider.value}")
            return None, None
        
        # Find next available key using round-robin
        attempts = 0
        while attempts < len(pool):
            index = self.rotation_index[provider]
            key_info = pool[index]
            
            # Move to next key for next request
            self.rotation_index[provider] = (index + 1) % len(pool)
            attempts += 1
            
            # Check if key is available
            if await self._is_key_available(key_info, model):
                # Decrypt the key
                try:
                    decrypted_key = self.cipher.decrypt(key_info.encrypted_key.encode()).decode()
                    
                    # Update usage
                    await self._update_key_usage(key_info.key_id)
                    
                    return decrypted_key, key_info.key_id
                    
                except Exception as e:
                    logger.error(f"Failed to decrypt key {key_info.key_id}: {e}")
                    continue
        
        logger.warning(f"No available API keys for provider: {provider.value}")
        return None, None
    
    async def _is_key_available(self, key_info: APIKeyInfo, model: Optional[str] = None) -> bool:
        """Check if an API key is available for use"""
        
        # Check status
        if key_info.status != KeyStatus.ACTIVE:
            return False
        
        # Check rate limit reset
        now = datetime.utcnow()
        if key_info.rate_limit_reset and now < key_info.rate_limit_reset:
            return False
        
        # Check daily quota
        if key_info.daily_quota:
            # Reset daily usage if new day
            if key_info.last_used and key_info.last_used.date() < now.date():
                key_info.daily_usage = 0
            
            if key_info.daily_usage >= key_info.daily_quota:
                return False
        
        # Check monthly quota
        if key_info.monthly_quota:
            # Reset monthly usage if new month
            if (key_info.last_used and 
                (key_info.last_used.year, key_info.last_used.month) < (now.year, now.month)):
                key_info.monthly_usage = 0
            
            if key_info.monthly_usage >= key_info.monthly_quota:
                return False
        
        # Check cost limit
        if key_info.cost_limit_usd and key_info.cost_used_usd >= key_info.cost_limit_usd:
            return False
        
        return True
    
    async def _update_key_usage(self, key_id: str):
        """Update key usage timestamp"""
        now = datetime.utcnow()
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE api_keys 
                SET usage_count = usage_count + 1, last_used = ?, updated_at = ?
                WHERE key_id = ?
            """, (now.isoformat(), now.isoformat(), key_id))
            await db.commit()
        
        # Update in-memory copy
        for provider_pool in self.key_pools.values():
            for key_info in provider_pool:
                if key_info.key_id == key_id:
                    key_info.usage_count += 1
                    key_info.last_used = now
                    key_info.updated_at = now
                    break
    
    async def record_usage(
        self,
        key_id: str,
        provider: LLMProvider,
        model_name: str,
        tokens_used: int,
        response_time_ms: int,
        success: bool,
        error_message: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Record API usage for tracking and billing
        
        Args:
            key_id: API key ID
            provider: LLM provider
            model_name: Model used
            tokens_used: Number of tokens consumed
            response_time_ms: Response time in milliseconds
            success: Whether the request was successful
            error_message: Error message if failed
            metadata: Additional metadata
            
        Returns:
            str: Usage record ID
        """
        await self.initialize()
        
        # Calculate cost
        cost_usd = self._calculate_cost(provider, model_name, tokens_used)
        
        # Create usage record
        record_id = f"usage_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{tokens_used}"
        usage_record = UsageRecord(
            record_id=record_id,
            key_id=key_id,
            provider=provider,
            model_name=model_name,
            tokens_used=tokens_used,
            cost_usd=cost_usd,
            request_timestamp=datetime.utcnow(),
            response_time_ms=response_time_ms,
            success=success,
            error_message=error_message,
            metadata=metadata or {}
        )
        
        # Save to database
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO usage_records (
                    record_id, key_id, provider, model_name, tokens_used,
                    cost_usd, request_timestamp, response_time_ms, success,
                    error_message, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                usage_record.record_id, usage_record.key_id, usage_record.provider.value,
                usage_record.model_name, usage_record.tokens_used, usage_record.cost_usd,
                usage_record.request_timestamp.isoformat(), usage_record.response_time_ms,
                usage_record.success, usage_record.error_message,
                json.dumps(usage_record.metadata)
            ))
            
            # Update key usage counters
            await db.execute("""
                UPDATE api_keys 
                SET daily_usage = daily_usage + ?, 
                    monthly_usage = monthly_usage + ?,
                    cost_used_usd = cost_used_usd + ?,
                    updated_at = ?
                WHERE key_id = ?
            """, (tokens_used, tokens_used, cost_usd, datetime.utcnow().isoformat(), key_id))
            
            await db.commit()
        
        logger.debug(f"Recorded usage: {record_id} - {tokens_used} tokens, ${cost_usd:.4f}")
        return record_id
    
    def _calculate_cost(self, provider: LLMProvider, model_name: str, tokens_used: int) -> float:
        """Calculate cost for API usage"""
        
        config = self.provider_configs.get(provider, {})
        costs = config.get("cost_per_1k_tokens", {})
        
        cost_per_1k = costs.get(model_name, 0.001)  # Default fallback
        return (tokens_used / 1000) * cost_per_1k
    
    async def handle_rate_limit(self, key_id: str, reset_time: Optional[datetime] = None):
        """Handle rate limit for a specific key"""
        
        if not reset_time:
            reset_time = datetime.utcnow() + timedelta(minutes=1)  # Default 1 minute
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE api_keys 
                SET status = ?, rate_limit_reset = ?, updated_at = ?
                WHERE key_id = ?
            """, (KeyStatus.RATE_LIMITED.value, reset_time.isoformat(), 
                  datetime.utcnow().isoformat(), key_id))
            await db.commit()
        
        # Update in-memory copy
        for provider_pool in self.key_pools.values():
            for key_info in provider_pool:
                if key_info.key_id == key_id:
                    key_info.status = KeyStatus.RATE_LIMITED
                    key_info.rate_limit_reset = reset_time
                    break
        
        logger.warning(f"Key {key_id} rate limited until {reset_time}")
    
    async def get_usage_statistics(
        self,
        provider: Optional[LLMProvider] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get usage statistics"""
        
        await self.initialize()
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        conditions = ["request_timestamp >= ?"]
        params = [cutoff_date.isoformat()]
        
        if provider:
            conditions.append("provider = ?")
            params.append(provider.value)
        
        where_clause = " AND ".join(conditions)
        
        stats = {}
        async with aiosqlite.connect(self.db_path) as db:
            # Total usage
            async with db.execute(f"""
                SELECT COUNT(*) as total_requests,
                       SUM(tokens_used) as total_tokens,
                       SUM(cost_usd) as total_cost,
                       AVG(response_time_ms) as avg_response_time
                FROM usage_records
                WHERE {where_clause}
            """, params) as cursor:
                row = await cursor.fetchone()
                if row:
                    stats.update({
                        "total_requests": row[0],
                        "total_tokens": row[1] or 0,
                        "total_cost": row[2] or 0.0,
                        "avg_response_time_ms": row[3] or 0.0
                    })
            
            # Usage by provider
            stats["by_provider"] = {}
            async with db.execute(f"""
                SELECT provider, COUNT(*) as requests, SUM(tokens_used) as tokens, SUM(cost_usd) as cost
                FROM usage_records
                WHERE {where_clause}
                GROUP BY provider
            """, params) as cursor:
                async for row in cursor:
                    stats["by_provider"][row[0]] = {
                        "requests": row[1],
                        "tokens": row[2] or 0,
                        "cost": row[3] or 0.0
                    }
            
            # Usage by model
            stats["by_model"] = {}
            async with db.execute(f"""
                SELECT model_name, COUNT(*) as requests, SUM(tokens_used) as tokens, SUM(cost_usd) as cost
                FROM usage_records
                WHERE {where_clause}
                GROUP BY model_name
            """, params) as cursor:
                async for row in cursor:
                    stats["by_model"][row[0]] = {
                        "requests": row[1],
                        "tokens": row[2] or 0,
                        "cost": row[3] or 0.0
                    }
        
        return stats
    
    async def list_api_keys(self, provider: Optional[LLMProvider] = None) -> List[Dict[str, Any]]:
        """List API keys (without exposing actual keys)"""
        
        await self.initialize()
        
        keys = []
        for prov, pool in self.key_pools.items():
            if provider and prov != provider:
                continue
            
            for key_info in pool:
                keys.append({
                    "key_id": key_info.key_id,
                    "provider": key_info.provider.value,
                    "status": key_info.status.value,
                    "usage_count": key_info.usage_count,
                    "last_used": key_info.last_used.isoformat() if key_info.last_used else None,
                    "daily_quota": key_info.daily_quota,
                    "daily_usage": key_info.daily_usage,
                    "monthly_quota": key_info.monthly_quota,
                    "monthly_usage": key_info.monthly_usage,
                    "cost_limit_usd": key_info.cost_limit_usd,
                    "cost_used_usd": key_info.cost_used_usd,
                    "priority": key_info.priority,
                    "created_at": key_info.created_at.isoformat()
                })
        
        return keys
    
    async def remove_api_key(self, key_id: str) -> bool:
        """Remove an API key"""
        
        await self.initialize()
        
        async with aiosqlite.connect(self.db_path) as db:
            # Check if key exists
            async with db.execute("SELECT provider FROM api_keys WHERE key_id = ?", (key_id,)) as cursor:
                row = await cursor.fetchone()
                if not row:
                    return False
                
                provider = LLMProvider(row[0])
            
            # Remove from database
            await db.execute("DELETE FROM api_keys WHERE key_id = ?", (key_id,))
            await db.execute("DELETE FROM usage_records WHERE key_id = ?", (key_id,))
            await db.commit()
        
        # Remove from memory pool
        self.key_pools[provider] = [k for k in self.key_pools[provider] if k.key_id != key_id]
        
        logger.info(f"Removed API key: {key_id}")
        return True
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("API Key Manager cleanup completed")


# Global API key manager instance
api_key_manager = APIKeyManager()