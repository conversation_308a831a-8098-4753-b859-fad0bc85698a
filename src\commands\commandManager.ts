import * as vscode from 'vscode';
import { serviceRegistry } from '../utils/serviceRegistry';

export class CommandManager implements vscode.Disposable {
  private disposables: vscode.Disposable[] = [];

  constructor(private context: vscode.ExtensionContext) {}

  public registerCommands(): void {
    this.registerCommand('metamorphic-reactor.startTask', this.startTask.bind(this));
    this.registerCommand('metamorphic-reactor.stopTask', this.stopTask.bind(this));
    this.registerCommand('metamorphic-reactor.openDashboard', this.openDashboard.bind(this));
  }

  private registerCommand(command: string, callback: (...args: any[]) => any): void {
    const disposable = vscode.commands.registerCommand(command, callback);
    this.disposables.push(disposable);
    this.context.subscriptions.push(disposable);
  }

  private async startTask(): Promise<void> {
    try {
      // Get WebView provider from service registry
      const webViewProvider = serviceRegistry.getWebViewProvider();
      
      if (webViewProvider) {
        // Open/show the WebView panel
        await webViewProvider.createOrShow();
        
        // Show success message
        vscode.window.showInformationMessage('Metamorphic Task panel opened');
      } else {
        // Fallback: show info and guide user
        const action = await vscode.window.showInformationMessage(
          'Metamorphic Reactor is starting up...',
          'Open Dashboard'
        );
        
        if (action === 'Open Dashboard') {
          vscode.commands.executeCommand('metamorphic-reactor.openDashboard');
        }
      }
      
    } catch (error) {
      console.error('Error starting task:', error);
      vscode.window.showErrorMessage(`Failed to start Metamorphic Task: ${error}`);
    }
  }

  private async stopTask(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Stopping Metamorphic Task...');
      
      // TODO: Implement task termination logic
      // - Gracefully shutdown worker threads
      // - Save current state
      // - Close WebView panel
      
    } catch (error) {
      console.error('Error stopping task:', error);
      vscode.window.showErrorMessage('Failed to stop Metamorphic Task');
    }
  }

  private async openDashboard(): Promise<void> {
    try {
      // Get WebView provider from service registry
      const webViewProvider = serviceRegistry.getWebViewProvider();
      
      if (webViewProvider) {
        await webViewProvider.createOrShow();
        vscode.window.showInformationMessage('Dashboard opened');
      } else {
        vscode.window.showWarningMessage('Dashboard not available - extension initializing');
      }
      
    } catch (error) {
      console.error('Error opening dashboard:', error);
      vscode.window.showErrorMessage(`Failed to open dashboard: ${error}`);
    }
  }

  public dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}