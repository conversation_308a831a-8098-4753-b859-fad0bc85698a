<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agent: any;
  export let layout: 'grid' | 'list' | 'compact' = 'grid';
  export let showActions: boolean = true;
  export let showMetrics: boolean = true;
  export let allowEdit: boolean = true;
  export let allowDelete: boolean = true;

  let outputElement: HTMLElement;
  let isStreaming = false;
  let showEditModal = false;
  let showDeleteConfirm = false;
  let deleteStep = 1; // 1: confirm, 2: backup, 3: final confirm
  let deleteReason = '';
  let createBackup = true;
  let deleteConfirmText = '';
  let isDeleting = false;
  let deleteError = '';

  // Inline editing variables
  let enableInlineEdit = true;
  let editingName = false;
  let tempName = '';

  // Enhanced agent interface
  interface Agent {
    id: string;
    name: string;
    role: string;
    model: string;
    status: 'active' | 'inactive' | 'draft' | 'error';
    capabilities: string[];
    team?: string;
    created: string;
    lastModified?: string;
    usage?: {
      totalRequests: number;
      successRate: number;
      avgResponseTime: number;
      lastUsed?: string;
    };
    metadata?: {
      description?: string;
      tags?: string[];
      version?: string;
      author?: string;
    };
    prompt?: string;
  }

  $: statusConfig = {
    active: { color: '#10b981', icon: '🟢', label: 'Active' },
    inactive: { color: '#6b7280', icon: '⚫', label: 'Inactive' },
    draft: { color: '#f59e0b', icon: '📝', label: 'Draft' },
    error: { color: '#ef4444', icon: '❌', label: 'Error' }
  }[agent.status] || { color: '#6b7280', icon: '❓', label: 'Unknown' };

  $: roleConfig = {
    coder: { color: '#10b981', icon: '💻', description: 'Code generation and debugging' },
    debugger: { color: '#f59e0b', icon: '🔍', description: 'Bug analysis and fixing' },
    pm: { color: '#3b82f6', icon: '📋', description: 'Project management' },
    ux: { color: '#8b5cf6', icon: '🎨', description: 'User experience design' },
    qa: { color: '#ef4444', icon: '✅', description: 'Quality assurance' },
    analyst: { color: '#06b6d4', icon: '📊', description: 'Data analysis' },
    researcher: { color: '#84cc16', icon: '🔬', description: 'Research and analysis' },
    writer: { color: '#f97316', icon: '✍️', description: 'Content creation' }
  }[agent.role?.toLowerCase()] || { color: '#6b7280', icon: '🤖', description: 'AI Agent' };

  $: isStreaming = agent.status === 'running';

  onMount(() => {
    // Listen for token streams for this specific agent
    window.addEventListener('message', handleTokenStream);
    
    return () => {
      window.removeEventListener('message', handleTokenStream);
    };
  });

  function handleTokenStream(event: MessageEvent) {
    const message = event.data;
    
    if (message.command === 'tokenStream' && message.agentId === agent.id) {
      if (outputElement) {
        outputElement.textContent += message.token;
        outputElement.scrollTop = outputElement.scrollHeight;
      }
    }
    
    if (message.command === 'clearOutput') {
      if (outputElement) {
        outputElement.textContent = '';
      }
    }
  }

  function handleAction(action: string) {
    dispatch('action', { action, agent });
  }

  function handleEdit() {
    showEditModal = true;
    dispatch('action', { action: 'edit', agent });
  }

  function handleDelete() {
    showDeleteConfirm = true;
    deleteStep = 1;
    deleteReason = '';
    createBackup = true;
    deleteConfirmText = '';
    isDeleting = false;
    deleteError = '';
  }

  function confirmDelete() {
    if (deleteStep === 1) {
      deleteStep = 2;
    } else if (deleteStep === 2) {
      deleteStep = 3;
    } else if (deleteStep === 3) {
      if (deleteConfirmText !== agent.name) {
        deleteError = 'Agent name does not match';
        return;
      }
      
      isDeleting = true;
      deleteError = '';
      
      dispatch('action', { 
        action: 'delete', 
        agent,
        options: {
          reason: deleteReason,
          createBackup,
          confirmed: true
        }
      });
      
      // Reset after a delay to show the deletion is in progress
      setTimeout(() => {
        showDeleteConfirm = false;
        isDeleting = false;
      }, 1000);
    }
  }

  function cancelDelete() {
    if (deleteStep > 1) {
      deleteStep--;
    } else {
      showDeleteConfirm = false;
    }
  }

  function handleStart() {
    dispatch('action', { action: 'start', agent });
  }

  function handleStop() {
    dispatch('action', { action: 'stop', agent });
  }

  function handleTest() {
    dispatch('action', { action: 'test', agent });
  }

  function handleClone() {
    dispatch('action', { action: 'clone', agent });
  }

  function handleExport() {
    const exportData = {
      ...agent,
      exported: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${agent.name.replace(/\s+/g, '-').toLowerCase()}-agent.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    dispatch('action', { action: 'export', agent });
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }

  function formatRelativeTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return formatDate(dateString);
  }

  function getCapabilityColor(capability: string): string {
    let hash = 0;
    for (let i = 0; i < capability.length; i++) {
      hash = capability.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
    return colors[Math.abs(hash) % colors.length];
  }

  // Inline editing functions
  function startInlineEdit(field: string) {
    if (field === 'name') {
      editingName = true;
      tempName = agent.name || '';
    }
  }

  function saveInlineEdit(field: string) {
    if (field === 'name') {
      if (tempName.trim() && tempName !== agent.name) {
        dispatch('action', {
          action: 'update',
          agent: { ...agent, name: tempName.trim() }
        });
      }
      editingName = false;
    }
  }

  function cancelInlineEdit(field: string) {
    if (field === 'name') {
      editingName = false;
      tempName = '';
    }
  }

  function handleKeydown(event: KeyboardEvent, field: string) {
    if (event.key === 'Enter') {
      event.preventDefault();
      saveInlineEdit(field);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      cancelInlineEdit(field);
    }
  }
</script>

{#if layout === 'grid'}
  <!-- Grid Layout -->
  <div class="agent-card grid">
    <div class="card-header">
      <div class="agent-info">
        <div class="agent-title">
          <div class="role-indicator" style="background-color: {roleConfig.color}">
            {roleConfig.icon}
          </div>
          <div class="title-content">
            {#if editingName}
              <div class="inline-edit-field">
                <input
                  type="text"
                  bind:value={tempName}
                  on:keydown={(e) => handleKeydown(e, 'name')}
                  on:blur={() => saveInlineEdit('name')}
                  class="edit-input name-input"
                  placeholder="Agent name"
                  autofocus
                />
                <div class="edit-actions">
                  <button class="edit-btn save" on:click={() => saveInlineEdit('name')}>✓</button>
                  <button class="edit-btn cancel" on:click={() => cancelInlineEdit('name')}>✗</button>
                </div>
              </div>
            {:else}
              <h3 class="agent-name" class:editable={enableInlineEdit && allowEdit} on:click={() => startInlineEdit('name')}>
                {agent.name || agent.id}
                {#if enableInlineEdit && allowEdit}
                  <span class="edit-hint">✏️</span>
                {/if}
              </h3>
            {/if}
            <span class="agent-role">{agent.role}</span>
          </div>
        </div>
        <div class="status-indicator" style="color: {statusConfig.color}">
          <span class="status-icon">{statusConfig.icon}</span>
          <span class="status-label">{statusConfig.label}</span>
        </div>
      </div>
      
      {#if showActions}
        <div class="card-actions">
          <div class="action-dropdown">
            <button type="button" class="action-btn dropdown-trigger">⋮</button>
            <div class="dropdown-menu">
              {#if allowEdit}
                <button type="button" class="dropdown-item" on:click={handleEdit}>
                  ✏️ Edit
                </button>
              {/if}
              <button type="button" class="dropdown-item" on:click={handleTest}>
                🧪 Test
              </button>
              <button type="button" class="dropdown-item" on:click={handleClone}>
                📋 Clone
              </button>
              <button type="button" class="dropdown-item" on:click={handleExport}>
                📤 Export
              </button>
              {#if agent.status === 'active'}
                <button type="button" class="dropdown-item" on:click={handleStop}>
                  ⏹️ Stop
                </button>
              {:else}
                <button type="button" class="dropdown-item" on:click={handleStart}>
                  ▶️ Start
                </button>
              {/if}
              {#if allowDelete}
                <div class="dropdown-divider"></div>
                <button type="button" class="dropdown-item danger" on:click={handleDelete}>
                  🗑️ Delete
                </button>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    </div>
    
    <div class="card-content">
      <!-- Model and Team Info -->
      <div class="agent-details">
        <div class="detail-item">
          <span class="detail-label">Model:</span>
          <span class="detail-value">{agent.model || 'Not specified'}</span>
        </div>
        {#if agent.team}
          <div class="detail-item">
            <span class="detail-label">Team:</span>
            <span class="detail-value">{agent.team}</span>
          </div>
        {/if}
      </div>
      
      <!-- Description -->
      {#if agent.metadata?.description}
        <div class="agent-description">
          <p>{agent.metadata.description}</p>
        </div>
      {:else}
        <div class="agent-description">
          <p class="description-placeholder">{roleConfig.description}</p>
        </div>
      {/if}
      
      <!-- Capabilities -->
      {#if agent.capabilities && agent.capabilities.length > 0}
        <div class="capabilities-section">
          <div class="capabilities-list">
            {#each agent.capabilities.slice(0, 3) as capability}
              <span 
                class="capability-tag"
                style="background-color: {getCapabilityColor(capability)}"
              >
                {capability}
              </span>
            {/each}
            {#if agent.capabilities.length > 3}
              <span class="more-capabilities">+{agent.capabilities.length - 3}</span>
            {/if}
          </div>
        </div>
      {/if}
      
      <!-- Usage Metrics -->
      {#if showMetrics && agent.usage}
        <div class="metrics-section">
          <div class="metrics-grid">
            <div class="metric-item">
              <span class="metric-value">{agent.usage.totalRequests || 0}</span>
              <span class="metric-label">Requests</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{agent.usage.successRate || 0}%</span>
              <span class="metric-label">Success</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{agent.usage.avgResponseTime || 0}ms</span>
              <span class="metric-label">Response</span>
            </div>
          </div>
        </div>
      {/if}
      
      <!-- Output Stream (for running agents) -->
      {#if isStreaming}
        <div class="output-section">
          <div class="output-header">
            <span class="output-label">Output</span>
            <div class="streaming-indicator">
              <div class="pulse-dot"></div>
              Streaming...
            </div>
          </div>
          <div class="output-content">
            <div 
              bind:this={outputElement}
              class="output-text" 
              id="stream-{agent.id}"
            >
              <div class="streaming-placeholder">
                <div class="streaming-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                Thinking...
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
    
    <div class="card-footer">
      <div class="footer-info">
        <span class="created-date">
          Created {formatRelativeTime(agent.created)}
        </span>
        {#if agent.usage?.lastUsed}
          <span class="last-used">
            Last used {formatRelativeTime(agent.usage.lastUsed)}
          </span>
        {/if}
      </div>
      
      {#if agent.metadata?.version}
        <span class="version-badge">v{agent.metadata.version}</span>
      {/if}
    </div>
  </div>
  
{:else if layout === 'list'}
  <!-- List Layout -->
  <div class="agent-card list">
    <div class="list-content">
      <div class="list-left">
        <div class="agent-title">
          <div class="role-indicator" style="background-color: {roleConfig.color}">
            {roleConfig.icon}
          </div>
          <div class="title-content">
            {#if editingName}
              <div class="inline-edit-field">
                <input
                  type="text"
                  bind:value={tempName}
                  on:keydown={(e) => handleKeydown(e, 'name')}
                  on:blur={() => saveInlineEdit('name')}
                  class="edit-input name-input"
                  placeholder="Agent name"
                  autofocus
                />
                <div class="edit-actions">
                  <button class="edit-btn save" on:click={() => saveInlineEdit('name')}>✓</button>
                  <button class="edit-btn cancel" on:click={() => cancelInlineEdit('name')}>✗</button>
                </div>
              </div>
            {:else}
              <h3 class="agent-name" class:editable={enableInlineEdit && allowEdit} on:click={() => startInlineEdit('name')}>
                {agent.name || agent.id}
                {#if enableInlineEdit && allowEdit}
                  <span class="edit-hint">✏️</span>
                {/if}
              </h3>
            {/if}
            <div class="agent-meta">
              <span class="agent-role">{agent.role}</span>
              <span class="agent-model">{agent.model}</span>
              {#if agent.team}
                <span class="agent-team">{agent.team}</span>
              {/if}
            </div>
          </div>
        </div>
      </div>
      
      <div class="list-center">
        <div class="capabilities-inline">
          {#each (agent.capabilities || []).slice(0, 4) as capability}
            <span class="capability-tag small">{capability}</span>
          {/each}
          {#if agent.capabilities && agent.capabilities.length > 4}
            <span class="more-capabilities">+{agent.capabilities.length - 4}</span>
          {/if}
        </div>
        
        {#if showMetrics && agent.usage}
          <div class="metrics-inline">
            <span class="metric">{agent.usage.totalRequests || 0} requests</span>
            <span class="metric">{agent.usage.successRate || 0}% success</span>
            <span class="metric">{agent.usage.avgResponseTime || 0}ms avg</span>
          </div>
        {/if}
      </div>
      
      <div class="list-right">
        <div class="status-section">
          <div class="status-indicator" style="color: {statusConfig.color}">
            <span class="status-icon">{statusConfig.icon}</span>
            <span class="status-label">{statusConfig.label}</span>
          </div>
          <span class="created-date">{formatRelativeTime(agent.created)}</span>
        </div>
        
        {#if showActions}
          <div class="list-actions">
            {#if allowEdit}
              <button type="button" class="action-btn" on:click={handleEdit} title="Edit">
                ✏️
              </button>
            {/if}
            <button type="button" class="action-btn" on:click={handleTest} title="Test">
              🧪
            </button>
            {#if agent.status === 'active'}
              <button type="button" class="action-btn" on:click={handleStop} title="Stop">
                ⏹️
              </button>
            {:else}
              <button type="button" class="action-btn" on:click={handleStart} title="Start">
                ▶️
              </button>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
  
{:else if layout === 'compact'}
  <!-- Compact Layout -->
  <div class="agent-card compact">
    <div class="compact-content">
      <div class="role-indicator" style="background-color: {roleConfig.color}">
        {roleConfig.icon}
      </div>
      <div class="compact-info">
        {#if editingName}
          <div class="inline-edit-field">
            <input
              type="text"
              bind:value={tempName}
              on:keydown={(e) => handleKeydown(e, 'name')}
              on:blur={() => saveInlineEdit('name')}
              class="edit-input name-input"
              placeholder="Agent name"
              autofocus
            />
            <div class="edit-actions">
              <button class="edit-btn save" on:click={() => saveInlineEdit('name')}>✓</button>
              <button class="edit-btn cancel" on:click={() => cancelInlineEdit('name')}>✗</button>
            </div>
          </div>
        {:else}
          <h4 class="agent-name" class:editable={enableInlineEdit && allowEdit} on:click={() => startInlineEdit('name')}>
            {agent.name || agent.id}
            {#if enableInlineEdit && allowEdit}
              <span class="edit-hint">✏️</span>
            {/if}
          </h4>
        {/if}
        <span class="agent-role">{agent.role}</span>
      </div>
      <div class="status-indicator" style="color: {statusConfig.color}">
        {statusConfig.icon}
      </div>
      {#if showActions && allowEdit}
        <button type="button" class="action-btn compact" on:click={handleEdit}>
          ✏️
        </button>
      {/if}
    </div>
  </div>
{/if}

<!-- Enhanced Delete Confirmation Modal -->
{#if showDeleteConfirm}
  <div class="modal-overlay" on:click={cancelDelete}>
    <div class="modal-content delete-modal" on:click|stopPropagation>
      <div class="modal-header">
        <h3>
          {#if deleteStep === 1}
            Delete Agent
          {:else if deleteStep === 2}
            Backup Options
          {:else}
            Final Confirmation
          {/if}
        </h3>
        <button type="button" class="close-btn" on:click={cancelDelete}>×</button>
      </div>
      
      <div class="modal-body">
        {#if deleteStep === 1}
          <!-- Step 1: Initial Confirmation -->
          <div class="delete-step">
            <div class="agent-preview">
              <div class="agent-preview-header">
                <div class="role-indicator" style="background-color: {roleConfig.color}">
                  {roleConfig.icon}
                </div>
                <div class="agent-info">
                  <h4>{agent.name || agent.id}</h4>
                  <p>{agent.role} • {agent.model}</p>
                </div>
              </div>
              <div class="agent-stats">
                <div class="stat">
                  <span class="stat-label">Status:</span>
                  <span class="stat-value">{agent.status}</span>
                </div>
                <div class="stat">
                  <span class="stat-label">Requests:</span>
                  <span class="stat-value">{agent.usage?.totalRequests || 0}</span>
                </div>
                <div class="stat">
                  <span class="stat-label">Created:</span>
                  <span class="stat-value">{formatDate(agent.created)}</span>
                </div>
              </div>
            </div>
            
            <div class="warning-section">
              <div class="warning-icon">⚠️</div>
              <div class="warning-content">
                <p class="warning-title">This action will permanently delete this agent</p>
                <ul class="warning-list">
                  <li>All agent configurations will be lost</li>
                  <li>Conversation history will be deleted</li>
                  <li>Any running tasks will be terminated</li>
                  <li>Associated metrics will be removed</li>
                </ul>
              </div>
            </div>
            
            <div class="reason-section">
              <label for="delete-reason">Reason for deletion (optional):</label>
              <textarea
                id="delete-reason"
                bind:value={deleteReason}
                placeholder="Why are you deleting this agent?"
                class="reason-textarea"
                rows="3"
              />
            </div>
          </div>
          
        {:else if deleteStep === 2}
          <!-- Step 2: Backup Options -->
          <div class="delete-step">
            <div class="backup-section">
              <h4>Would you like to create a backup?</h4>
              <p>Creating a backup allows you to restore this agent later if needed.</p>
              
              <div class="backup-options">
                <label class="backup-option">
                  <input type="radio" bind:group={createBackup} value={true} />
                  <div class="option-content">
                    <div class="option-title">Create Backup</div>
                    <div class="option-description">Export agent configuration to JSON file</div>
                  </div>
                </label>
                
                <label class="backup-option">
                  <input type="radio" bind:group={createBackup} value={false} />
                  <div class="option-content">
                    <div class="option-title">No Backup</div>
                    <div class="option-description">Delete immediately without backup</div>
                  </div>
                </label>
              </div>
              
              {#if createBackup}
                <div class="backup-details">
                  <div class="backup-info">
                    <div class="backup-icon">💾</div>
                    <div class="backup-text">
                      <p>Backup will include:</p>
                      <ul>
                        <li>Agent configuration and settings</li>
                        <li>Prompt templates and capabilities</li>
                        <li>Usage statistics and metrics</li>
                        <li>Metadata and version info</li>
                      </ul>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          </div>
          
        {:else}
          <!-- Step 3: Final Confirmation -->
          <div class="delete-step">
            <div class="final-warning">
              <div class="danger-icon">🚨</div>
              <div class="danger-text">
                <h4>This action cannot be undone!</h4>
                <p>To proceed, please type the agent name exactly as shown:</p>
                <p class="agent-name-display">{agent.name || agent.id}</p>
              </div>
            </div>
            
            <div class="confirm-input-section">
              <label for="confirm-input">Type agent name to confirm:</label>
              <input
                id="confirm-input"
                type="text"
                bind:value={deleteConfirmText}
                placeholder="Enter agent name"
                class="confirm-input"
                class:error={deleteError}
              />
              {#if deleteError}
                <div class="error-message">{deleteError}</div>
              {/if}
            </div>
            
            <div class="deletion-summary">
              <h4>Deletion Summary:</h4>
              <div class="summary-item">
                <span class="summary-label">Agent:</span>
                <span class="summary-value">{agent.name || agent.id}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Backup:</span>
                <span class="summary-value">{createBackup ? 'Yes' : 'No'}</span>
              </div>
              {#if deleteReason}
                <div class="summary-item">
                  <span class="summary-label">Reason:</span>
                  <span class="summary-value">{deleteReason}</span>
                </div>
              {/if}
            </div>
          </div>
        {/if}
      </div>
      
      <div class="modal-actions">
        <button type="button" class="btn-secondary" on:click={cancelDelete}>
          {deleteStep > 1 ? 'Back' : 'Cancel'}
        </button>
        
        <button 
          type="button" 
          class="btn-danger" 
          class:loading={isDeleting}
          disabled={isDeleting || (deleteStep === 3 && deleteConfirmText !== agent.name)}
          on:click={confirmDelete}
        >
          {#if isDeleting}
            Deleting...
          {:else if deleteStep === 1}
            Continue
          {:else if deleteStep === 2}
            {createBackup ? 'Backup & Continue' : 'Continue'}
          {:else}
            Delete Agent
          {/if}
        </button>
      </div>
      
      <!-- Progress Indicator -->
      <div class="progress-indicator">
        <div class="progress-step" class:active={deleteStep >= 1} class:completed={deleteStep > 1}>1</div>
        <div class="progress-line" class:active={deleteStep >= 2}></div>
        <div class="progress-step" class:active={deleteStep >= 2} class:completed={deleteStep > 2}>2</div>
        <div class="progress-line" class:active={deleteStep >= 3}></div>
        <div class="progress-step" class:active={deleteStep >= 3}>3</div>
      </div>
    </div>
  </div>
{/if}

<style>
  .agent-card {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    transition: all 0.2s;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .agent-card:hover {
    border-color: #475569;
  }
  
  .agent-card.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }
  
  .checkbox-container {
    display: flex;
    align-items: center;
    margin-right: 12px;
  }
  
  .agent-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
    cursor: pointer;
  }
  
  /* Grid Layout */
  .agent-card.grid {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }
  
  .agent-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }
  
  .agent-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .role-indicator {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
  }
  
  .title-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .agent-name {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
    position: relative;
  }
  
  .agent-name.editable {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .agent-name.editable:hover {
    background: rgba(59, 130, 246, 0.1);
  }
  
  .edit-hint {
    opacity: 0;
    font-size: 12px;
    margin-left: 4px;
    transition: opacity 0.2s;
  }
  
  .editable:hover .edit-hint {
    opacity: 0.7;
  }
  
  .agent-role {
    color: #94a3b8;
    font-size: 13px;
    text-transform: capitalize;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
  }
  
  .status-icon {
    font-size: 10px;
  }
  
  .card-actions {
    position: relative;
  }
  
  .action-dropdown {
    position: relative;
  }
  
  .dropdown-trigger {
    padding: 4px 8px;
    background: transparent;
    border: 1px solid #475569;
    border-radius: 4px;
    color: #94a3b8;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
  }
  
  .dropdown-trigger:hover {
    background: #334155;
    color: #ffffff;
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 10;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 6px;
    min-width: 120px;
    padding: 4px 0;
    margin-top: 4px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    display: none;
  }
  
  .action-dropdown:hover .dropdown-menu {
    display: block;
  }
  
  .dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 6px 12px;
    background: none;
    border: none;
    color: #e2e8f0;
    text-align: left;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.15s;
  }
  
  .dropdown-item:hover {
    background: #334155;
  }
  
  .dropdown-item.danger {
    color: #f87171;
  }
  
  .dropdown-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
  }
  
  .dropdown-divider {
    height: 1px;
    background: #334155;
    margin: 4px 0;
  }
  
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .agent-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .detail-label {
    color: #94a3b8;
    font-size: 12px;
  }
  
  .detail-value {
    color: #e2e8f0;
    font-size: 12px;
    font-weight: 500;
  }
  
  .agent-description p {
    margin: 0;
    color: #cbd5e1;
    font-size: 13px;
    line-height: 1.4;
  }
  
  .description-content {
    margin: 0;
    color: #cbd5e1;
    font-size: 13px;
    line-height: 1.4;
    position: relative;
  }
  
  .description-content.editable {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .description-content.editable:hover {
    background: rgba(59, 130, 246, 0.1);
  }
  
  .description-placeholder {
    color: #64748b !important;
    font-style: italic;
  }
  
  .capabilities-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .capabilities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .capability-tag {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    color: white;
    font-weight: 500;
  }
  
  .capability-tag.small {
    padding: 1px 4px;
    font-size: 10px;
  }
  
  .more-capabilities {
    padding: 2px 6px;
    background: #475569;
    color: #94a3b8;
    border-radius: 3px;
    font-size: 11px;
  }
  
  /* Inline Editing Styles */
  .inline-edit-field {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid #3b82f6;
    border-radius: 4px;
  }
  
  .edit-input {
    flex: 1;
    padding: 4px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
  }
  
  .edit-input:focus {
    border-color: #3b82f6;
  }
  
  .name-input {
    font-weight: 600;
    font-size: 16px;
  }
  
  .edit-textarea {
    flex: 1;
    padding: 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
    outline: none;
    resize: vertical;
    min-height: 60px;
  }
  
  .edit-textarea:focus {
    border-color: #3b82f6;
  }
  
  .edit-actions {
    display: flex;
    gap: 4px;
  }
  
  .edit-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.2s;
  }
  
  .edit-btn.save {
    background: #10b981;
    color: white;
  }
  
  .edit-btn.save:hover {
    background: #059669;
  }
  
  .edit-btn.cancel {
    background: #ef4444;
    color: white;
  }
  
  .edit-btn.cancel:hover {
    background: #dc2626;
  }
  
  .capabilities-edit {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid #3b82f6;
    border-radius: 4px;
  }
  
  .capabilities-edit-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .capability-tag.editable {
    position: relative;
    padding-right: 20px;
  }
  
  .remove-capability {
    position: absolute;
    top: -2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    font-size: 10px;
    line-height: 1;
    transition: background-color 0.2s;
  }
  
  .remove-capability:hover {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .add-capability {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .capability-input {
    flex: 1;
    padding: 4px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    outline: none;
  }
  
  .capability-input:focus {
    border-color: #3b82f6;
  }
  
  .add-btn {
    width: 24px;
    height: 24px;
    background: #10b981;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: background-color 0.2s;
  }
  
  .add-btn:hover {
    background: #059669;
  }
  
  .capabilities-list.editable {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    position: relative;
  }
  
  .capabilities-list.editable:hover {
    background: rgba(59, 130, 246, 0.1);
  }
  
  .metrics-section {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 12px;
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .metric-item {
    text-align: center;
  }
  
  .metric-value {
    display: block;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
  }
  
  .metric-label {
    display: block;
    color: #64748b;
    font-size: 10px;
    margin-top: 2px;
  }
  
  .output-section {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 12px;
  }
  
  .output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .output-label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
  }
  
  .streaming-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #10b981;
    font-size: 11px;
  }
  
  .pulse-dot {
    width: 6px;
    height: 6px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }
  
  .output-content {
    height: 80px;
    overflow-y: auto;
  }
  
  .output-text {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    color: #10b981;
    line-height: 1.3;
  }
  
  .streaming-placeholder {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
  }
  
  .streaming-dots {
    display: flex;
    gap: 2px;
  }
  
  .streaming-dots span {
    width: 4px;
    height: 4px;
    background: #64748b;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
  }
  
  .streaming-dots span:nth-child(1) { animation-delay: -0.32s; }
  .streaming-dots span:nth-child(2) { animation-delay: -0.16s; }
  
  @keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #334155;
  }
  
  .footer-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .created-date, .last-used {
    color: #64748b;
    font-size: 11px;
  }
  
  .version-badge {
    padding: 2px 6px;
    background: #334155;
    color: #94a3b8;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
  }
  
  /* List Layout */
  .agent-card.list {
    padding: 16px 20px;
  }
  
  .list-content {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .list-left {
    flex: 0 0 auto;
    min-width: 200px;
  }
  
  .list-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .list-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .agent-meta {
    display: flex;
    gap: 8px;
    font-size: 12px;
  }
  
  .agent-model, .agent-team {
    color: #64748b;
  }
  
  .capabilities-inline {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .metrics-inline {
    display: flex;
    gap: 12px;
  }
  
  .metric {
    color: #94a3b8;
    font-size: 12px;
  }
  
  .status-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }
  
  .list-actions {
    display: flex;
    gap: 4px;
  }
  
  .action-btn {
    padding: 4px 6px;
    background: transparent;
    border: 1px solid #475569;
    border-radius: 4px;
    color: #94a3b8;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.15s;
  }
  
  .action-btn:hover {
    background: #334155;
    color: #ffffff;
  }
  
  /* Compact Layout */
  .agent-card.compact {
    padding: 12px 16px;
  }
  
  .compact-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .compact-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .compact-content .agent-name {
    font-size: 14px;
  }
  
  .compact-content .role-indicator {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .action-btn.compact {
    padding: 4px;
    font-size: 10px;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    max-width: 400px;
    width: 90vw;
    overflow: hidden;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #334155;
  }
  
  .modal-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #94a3b8;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
  }
  
  .close-btn:hover {
    color: #ffffff;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-body p {
    margin: 0 0 8px 0;
    color: #e2e8f0;
    line-height: 1.4;
  }
  
  .warning-text {
    color: #f87171;
    font-size: 14px;
  }
  
  .modal-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid #334155;
  }
  
  .btn-secondary {
    padding: 8px 16px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 4px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.15s;
  }
  
  .btn-secondary:hover {
    background: #4b5563;
  }
  
  .btn-danger {
    padding: 8px 16px;
    background: #ef4444;
    border: 1px solid #ef4444;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.15s;
  }
  
  .btn-danger:hover {
    background: #dc2626;
  }
  
  .btn-danger:disabled {
    background: #6b7280;
    cursor: not-allowed;
  }
  
  .btn-danger.loading {
    opacity: 0.7;
  }
  
  .modal-content.delete-modal {
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  /* Enhanced Delete Modal Styles */
  .delete-step {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .agent-preview {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 16px;
  }
  
  .agent-preview-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .agent-preview-header .role-indicator {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .agent-preview-header .agent-info h4 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .agent-preview-header .agent-info p {
    margin: 0;
    color: #94a3b8;
    font-size: 14px;
  }
  
  .agent-stats {
    display: flex;
    gap: 16px;
  }
  
  .stat {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .stat-label {
    color: #64748b;
    font-size: 12px;
  }
  
  .stat-value {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
  }
  
  .warning-section {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
  }
  
  .warning-icon {
    font-size: 24px;
    flex-shrink: 0;
  }
  
  .warning-content {
    flex: 1;
  }
  
  .warning-title {
    margin: 0 0 8px 0;
    color: #fecaca;
    font-size: 14px;
    font-weight: 600;
  }
  
  .warning-list {
    margin: 0;
    padding-left: 16px;
    color: #fecaca;
    font-size: 13px;
    line-height: 1.4;
  }
  
  .warning-list li {
    margin-bottom: 4px;
  }
  
  .reason-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .reason-section label {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
  }
  
  .reason-textarea {
    padding: 8px 12px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #ffffff;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    outline: none;
  }
  
  .reason-textarea:focus {
    border-color: #3b82f6;
  }
  
  .reason-textarea::placeholder {
    color: #64748b;
  }
  
  .backup-section h4 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .backup-section p {
    margin: 0 0 16px 0;
    color: #94a3b8;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .backup-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .backup-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .backup-option:hover {
    border-color: #3b82f6;
  }
  
  .backup-option input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
  }
  
  .option-content {
    flex: 1;
  }
  
  .option-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .option-description {
    color: #94a3b8;
    font-size: 12px;
    line-height: 1.3;
  }
  
  .backup-details {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 6px;
    padding: 12px;
  }
  
  .backup-info {
    display: flex;
    gap: 12px;
  }
  
  .backup-icon {
    font-size: 20px;
    flex-shrink: 0;
  }
  
  .backup-text p {
    margin: 0 0 8px 0;
    color: #bfdbfe;
    font-size: 13px;
    font-weight: 500;
  }
  
  .backup-text ul {
    margin: 0;
    padding-left: 16px;
    color: #bfdbfe;
    font-size: 12px;
    line-height: 1.4;
  }
  
  .backup-text li {
    margin-bottom: 2px;
  }
  
  .final-warning {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.4);
    border-radius: 8px;
    margin-bottom: 16px;
  }
  
  .danger-icon {
    font-size: 32px;
    flex-shrink: 0;
  }
  
  .danger-text h4 {
    margin: 0 0 8px 0;
    color: #fecaca;
    font-size: 16px;
    font-weight: 600;
  }
  
  .danger-text p {
    margin: 0 0 8px 0;
    color: #fecaca;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .agent-name-display {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff !important;
    margin: 0 !important;
  }
  
  .confirm-input-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .confirm-input-section label {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
  }
  
  .confirm-input {
    padding: 8px 12px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
  }
  
  .confirm-input:focus {
    border-color: #3b82f6;
  }
  
  .confirm-input.error {
    border-color: #ef4444;
  }
  
  .confirm-input::placeholder {
    color: #64748b;
  }
  
  .error-message {
    color: #f87171;
    font-size: 12px;
    margin-top: 4px;
  }
  
  .deletion-summary {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 12px;
  }
  
  .deletion-summary h4 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }
  
  .summary-item:last-child {
    margin-bottom: 0;
  }
  
  .summary-label {
    color: #94a3b8;
    font-size: 12px;
  }
  
  .summary-value {
    color: #e2e8f0;
    font-size: 12px;
    font-weight: 500;
  }
  
  .progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border-top: 1px solid #334155;
    gap: 8px;
  }
  
  .progress-step {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #475569;
    color: #94a3b8;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s;
  }
  
  .progress-step.active {
    background: #3b82f6;
    color: #ffffff;
  }
  
  .progress-step.completed {
    background: #10b981;
    color: #ffffff;
  }
  
  .progress-line {
    width: 32px;
    height: 2px;
    background: #475569;
    transition: background-color 0.2s;
  }
  
  .progress-line.active {
    background: #3b82f6;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .inline-edit-field {
      flex-direction: column;
      align-items: stretch;
    }
    
    .edit-actions {
      align-self: flex-end;
    }
    
    .add-capability {
      flex-direction: column;
      gap: 8px;
    }
    
    .capabilities-edit-list {
      justify-content: center;
    }
  }
  
  @media (max-width: 600px) {
    .modal-content.delete-modal {
      max-width: 95vw;
      max-height: 95vh;
    }
    
    .agent-stats {
      flex-direction: column;
      gap: 8px;
    }
    
    .warning-section {
      flex-direction: column;
      gap: 8px;
    }
    
    .final-warning {
      flex-direction: column;
      gap: 8px;
    }
  }
</style>