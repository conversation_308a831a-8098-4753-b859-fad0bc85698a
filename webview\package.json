{"name": "metamorphic-reactor-webview", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^2.4.2", "@tsconfig/svelte": "^5.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "svelte": "^4.0.5", "svelte-check": "^3.4.3", "tailwindcss": "^3.3.0", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.4.5"}}