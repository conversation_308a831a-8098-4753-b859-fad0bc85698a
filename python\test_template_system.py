#!/usr/bin/env python3
"""
Test script for the Agent Template System
This script demonstrates the functionality of the comprehensive template system
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock the dependencies for testing
class MockBaseModel:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockField:
    def __init__(self, default=None, **kwargs):
        self.default = default
        self.kwargs = kwargs

# Mock pydantic imports
sys.modules['pydantic'] = type('MockPydantic', (), {
    'BaseModel': MockBaseModel,
    'Field': MockField
})()

try:
    from agents.agent_factory import (
        AgentFactory, 
        TemplateManager, 
        AgentTemplate,
        TemplateCategory,
        TemplateStatus,
        TemplateMetadata,
        TemplateUsageStats,
        TemplateRating,
        TemplateVersion
    )
    
    print("✓ Successfully imported template system classes")
    
    # Test basic template creation
    factory = AgentFactory()
    template_manager = factory.template_manager
    
    print("✓ Template system initialized successfully")
    
    # Test template search
    templates = template_manager.search_templates(query="code")
    print(f"✓ Found {len(templates)} templates matching 'code'")
    
    # Test template categories
    categories = template_manager.get_template_categories()
    print(f"✓ Available template categories: {[cat.value for cat in categories]}")
    
    # Test template statistics
    stats = template_manager.get_template_statistics()
    print(f"✓ Template statistics: {stats['total_templates']} total templates")
    
    print("\n🎉 All basic tests passed! Template system is working correctly.")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()