<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  export let taskRunning = false;
  
  let taskDescription = '';
  const dispatch = createEventDispatcher<{
    startTask: string;
    stopTask: void;
  }>();

  function handleStart() {
    if (!taskDescription.trim()) {
      alert('Please enter a task description');
      return;
    }
    dispatch('startTask', taskDescription);
  }

  function handleStop() {
    dispatch('stopTask');
  }

  function handleClear() {
    taskDescription = '';
  }
</script>

<div class="bg-gray-800 rounded-lg p-6 mb-8">
  <label for="taskDescription" class="block text-sm font-medium mb-2">
    Task Description:
  </label>
  
  <textarea
    id="taskDescription"
    bind:value={taskDescription}
    placeholder="Describe the task you want the agents to complete..."
    class="w-full h-24 bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
    disabled={taskRunning}
  ></textarea>
  
  <div class="flex gap-3 mt-4">
    <button
      on:click={handleStart}
      disabled={taskRunning}
      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-md font-medium transition-colors"
    >
      {taskRunning ? 'Running...' : 'Start Task'}
    </button>
    
    <button
      on:click={handleStop}
      disabled={!taskRunning}
      class="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-md font-medium transition-colors"
    >
      Stop Task
    </button>
    
    <button
      on:click={handleClear}
      disabled={taskRunning}
      class="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-md font-medium transition-colors"
    >
      Clear
    </button>
  </div>
</div>