# 🧬 Metamorphic Reactor

Multi-agent LLM orchestration VS Code extension that runs 2-5 autonomous agents in a recursive **Plan → Act → Critique → Revise** loop until they reach absolute, unanimous consensus.

## Features

- **Multi-Agent Consensus**: 2-5 LLM agents work together until 100% agreement
- **Real-time Streaming**: Live token streams in VS Code WebView
- **BYO API Keys**: Support for Gemini, Claude, GPT with automatic rotation
- **Meta-Block Escalation**: Human intervention when agents can't reach consensus
- **Git Integration**: Auto-commit with AI-generated messages on completion
- **Safety Guardrails**: Token limits, secret scrubbing, PII filtering

## Quick Start

### Prerequisites

- VS Code 1.74.0+
- Node.js 16+
- Python 3.9+

### Installation

1. **Clone and setup extension:**
   ```bash
   git clone <repo-url>
   cd metamorphic-reactor
   npm install
   npm run compile
   ```

2. **Setup Python microservice:**
   ```bash
   cd python
   pip install -r requirements.txt
   ```

3. **Configure API keys in VS Code settings:**
   ```json
   {
     "metamorphic-reactor.apiKeys": {
       "gemini": ["your-gemini-key-1", "your-gemini-key-2"],
       "claude": ["your-claude-key"],
       "openai": ["your-openai-key"]
     },
     "metamorphic-reactor.maxAgents": 2
   }
   ```

### Development

1. **Start Python service:**
   ```bash
   cd python
   python main.py
   ```

2. **Launch extension in VS Code:**
   - Press `F5` to open Extension Development Host
   - Run command: "Start Metamorphic Task"

### Testing

```bash
# TypeScript tests
npm test

# Python tests  
cd python
pytest
```

## Architecture

```
VS Code Extension (TypeScript)
├── WebView Panel (Svelte + Tailwind)
├── Worker Thread Manager
└── Command Handler
            │
            ▼
Python AutoGen Service (FastAPI)
├── Multi-Agent Orchestrator
├── API Key Pool Manager
├── Consensus Detection Engine
└── Safety Guardrails
            │
            ▼
External LLM Providers
├── Gemini (60 RPM)
├── Claude (varies)
└── OpenAI (varies)
```

## Subscription Tiers

- **Free**: 2 agents, 10 loops/day
- **Pro ($14/mo)**: 3 agents, unlimited loops
- **Dev+ ($20/mo)**: 5 agents, team features
- **Enterprise ($40+/seat)**: SSO, hosted proxy, SLA

## Safety Features

- 15-minute / 10-round loop cap
- Secret scrubbing and PII filtering
- Token counting and rate limiting
- Consensus requirement (no 0.95 shortcuts)
- Meta-Block for human intervention

## Contributing

1. Fork the repository
2. Create feature branch
3. Run tests: `npm test && cd python && pytest`
4. Submit pull request

## License

MIT License - see LICENSE file for details.