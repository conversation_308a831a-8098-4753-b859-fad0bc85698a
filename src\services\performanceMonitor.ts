export interface PerformanceMetrics {
  agentId: string;
  role: string;
  
  // Timing metrics
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  totalTasks: number;
  
  // Resource metrics
  memoryUsage: number;
  cpuUsage: number;
  
  // Message metrics
  messagesSent: number;
  messagesReceived: number;
  timeouts: number;
  errors: number;
  
  // Task metrics
  tasksCompleted: number;
  tasksWithConsensus: number;
  consensusRate: number;
  
  // Quality metrics
  averageTokensPerResponse: number;
  responseQualityScore: number;
  
  // Timestamp
  lastUpdated: number;
  startTime: number;
}

export interface SystemMetrics {
  totalAgents: number;
  activeAgents: number;
  systemMemoryUsage: number;
  systemCpuUsage: number;
  overallConsensusRate: number;
  averageTaskCompletionTime: number;
  totalTasksProcessed: number;
  uptime: number;
}

export class PerformanceMonitor {
  private agentMetrics: Map<string, PerformanceMetrics> = new Map();
  private systemStartTime: number = Date.now();
  private measurementInterval: NodeJS.Timeout | null = null;
  private responseTimes: Map<string, number[]> = new Map();
  private taskStartTimes: Map<string, number> = new Map();

  constructor() {
    this.startPerformanceMonitoring();
  }

  private startPerformanceMonitoring(): void {
    // Collect metrics every 30 seconds
    this.measurementInterval = setInterval(() => {
      this.collectSystemMetrics();
      this.cleanupOldData();
    }, 30000);
  }

  public initializeAgent(agentId: string, role: string): void {
    const metrics: PerformanceMetrics = {
      agentId,
      role,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      totalTasks: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      messagesSent: 0,
      messagesReceived: 0,
      timeouts: 0,
      errors: 0,
      tasksCompleted: 0,
      tasksWithConsensus: 0,
      consensusRate: 0,
      averageTokensPerResponse: 0,
      responseQualityScore: 0,
      lastUpdated: Date.now(),
      startTime: Date.now()
    };

    this.agentMetrics.set(agentId, metrics);
    this.responseTimes.set(agentId, []);
  }

  public recordMessageSent(agentId: string, messageId: string): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.messagesSent++;
      metrics.lastUpdated = Date.now();
      
      // Record start time for response time calculation
      this.taskStartTimes.set(messageId, Date.now());
    }
  }

  public recordMessageReceived(agentId: string, messageId?: string): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.messagesReceived++;
      metrics.lastUpdated = Date.now();
      
      // Calculate response time if we have the start time
      if (messageId) {
        const startTime = this.taskStartTimes.get(messageId);
        if (startTime) {
          const responseTime = Date.now() - startTime;
          this.recordResponseTime(agentId, responseTime);
          this.taskStartTimes.delete(messageId);
        }
      }
    }
  }

  public recordResponseTime(agentId: string, responseTime: number): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      const times = this.responseTimes.get(agentId) || [];
      times.push(responseTime);
      
      // Keep only last 100 response times
      if (times.length > 100) {
        times.shift();
      }
      
      this.responseTimes.set(agentId, times);
      
      // Update metrics
      metrics.minResponseTime = Math.min(metrics.minResponseTime, responseTime);
      metrics.maxResponseTime = Math.max(metrics.maxResponseTime, responseTime);
      metrics.averageResponseTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      metrics.lastUpdated = Date.now();
    }
  }

  public recordTaskStart(agentId: string, taskId: string): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.totalTasks++;
      metrics.lastUpdated = Date.now();
      this.taskStartTimes.set(taskId, Date.now());
    }
  }

  public recordTaskCompletion(agentId: string, taskId: string, hasConsensus: boolean, tokenCount?: number): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.tasksCompleted++;
      
      if (hasConsensus) {
        metrics.tasksWithConsensus++;
      }
      
      metrics.consensusRate = metrics.tasksWithConsensus / metrics.tasksCompleted;
      
      if (tokenCount) {
        // Update average tokens per response
        const currentAvg = metrics.averageTokensPerResponse;
        const completedTasks = metrics.tasksCompleted;
        metrics.averageTokensPerResponse = (currentAvg * (completedTasks - 1) + tokenCount) / completedTasks;
      }
      
      metrics.lastUpdated = Date.now();
      
      // Clean up task start time
      this.taskStartTimes.delete(taskId);
    }
  }

  public recordTimeout(agentId: string): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.timeouts++;
      metrics.lastUpdated = Date.now();
    }
  }

  public recordError(agentId: string): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.errors++;
      metrics.lastUpdated = Date.now();
    }
  }

  public updateResourceUsage(agentId: string, memoryUsage: number, cpuUsage: number): void {
    const metrics = this.agentMetrics.get(agentId);
    if (metrics) {
      metrics.memoryUsage = memoryUsage;
      metrics.cpuUsage = cpuUsage;
      metrics.lastUpdated = Date.now();
    }
  }

  public calculateQualityScore(agentId: string): number {
    const metrics = this.agentMetrics.get(agentId);
    if (!metrics || metrics.totalTasks === 0) return 0;

    // Quality score based on multiple factors
    const consensusWeight = 0.4;
    const responseTimeWeight = 0.2;
    const reliabilityWeight = 0.3;
    const efficiencyWeight = 0.1;

    // Consensus score (0-1)
    const consensusScore = metrics.consensusRate;

    // Response time score (inverse of normalized response time)
    const avgResponseTime = metrics.averageResponseTime;
    const responseTimeScore = avgResponseTime > 0 ? Math.max(0, 1 - (avgResponseTime / 10000)) : 1; // 10s as baseline

    // Reliability score (based on error rate)
    const errorRate = metrics.errors / Math.max(metrics.messagesReceived, 1);
    const reliabilityScore = Math.max(0, 1 - errorRate);

    // Efficiency score (based on timeout rate)
    const timeoutRate = metrics.timeouts / Math.max(metrics.messagesSent, 1);
    const efficiencyScore = Math.max(0, 1 - timeoutRate);

    const qualityScore = (
      consensusScore * consensusWeight +
      responseTimeScore * responseTimeWeight +
      reliabilityScore * reliabilityWeight +
      efficiencyScore * efficiencyWeight
    );

    // Update the stored quality score
    metrics.responseQualityScore = qualityScore;
    metrics.lastUpdated = Date.now();

    return qualityScore;
  }

  private collectSystemMetrics(): void {
    // Update system metrics for all agents
    for (const metrics of this.agentMetrics.values()) {
      this.calculateQualityScore(metrics.agentId);
    }
  }

  private cleanupOldData(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    
    // Clean up old task start times
    for (const [taskId, startTime] of this.taskStartTimes) {
      if (startTime < cutoffTime) {
        this.taskStartTimes.delete(taskId);
      }
    }
  }

  public getAgentMetrics(agentId: string): PerformanceMetrics | undefined {
    return this.agentMetrics.get(agentId);
  }

  public getAllAgentMetrics(): PerformanceMetrics[] {
    return Array.from(this.agentMetrics.values());
  }

  public getSystemMetrics(): SystemMetrics {
    const agents = Array.from(this.agentMetrics.values());
    const totalAgents = agents.length;
    const activeAgents = agents.filter(a => Date.now() - a.lastUpdated < 60000).length;

    const totalTasks = agents.reduce((sum, a) => sum + a.tasksCompleted, 0);
    const totalConsensus = agents.reduce((sum, a) => sum + a.tasksWithConsensus, 0);
    const overallConsensusRate = totalTasks > 0 ? totalConsensus / totalTasks : 0;

    const responseTimes = agents.map(a => a.averageResponseTime).filter(t => t > 0);
    const averageTaskCompletionTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length 
      : 0;

    const memUsage = process.memoryUsage();
    const systemMemoryUsage = memUsage.heapUsed;

    return {
      totalAgents,
      activeAgents,
      systemMemoryUsage,
      systemCpuUsage: 0, // Would need additional monitoring for accurate CPU usage
      overallConsensusRate,
      averageTaskCompletionTime,
      totalTasksProcessed: totalTasks,
      uptime: Date.now() - this.systemStartTime
    };
  }

  public getPerformanceReport(): {
    system: SystemMetrics;
    agents: PerformanceMetrics[];
    summary: {
      topPerformers: { agentId: string; score: number }[];
      slowestResponders: { agentId: string; avgTime: number }[];
      mostReliable: { agentId: string; errorRate: number }[];
    };
  } {
    const agents = this.getAllAgentMetrics();
    const system = this.getSystemMetrics();

    // Calculate summary statistics
    const topPerformers = agents
      .map(a => ({ agentId: a.agentId, score: a.responseQualityScore }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);

    const slowestResponders = agents
      .filter(a => a.averageResponseTime > 0)
      .map(a => ({ agentId: a.agentId, avgTime: a.averageResponseTime }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 3);

    const mostReliable = agents
      .map(a => ({ 
        agentId: a.agentId, 
        errorRate: a.messagesReceived > 0 ? a.errors / a.messagesReceived : 0 
      }))
      .sort((a, b) => a.errorRate - b.errorRate)
      .slice(0, 3);

    return {
      system,
      agents,
      summary: {
        topPerformers,
        slowestResponders,
        mostReliable
      }
    };
  }

  public removeAgent(agentId: string): void {
    this.agentMetrics.delete(agentId);
    this.responseTimes.delete(agentId);
    
    // Clean up any pending task times for this agent
    for (const [taskId, _] of this.taskStartTimes) {
      if (taskId.includes(agentId)) {
        this.taskStartTimes.delete(taskId);
      }
    }
  }

  public dispose(): void {
    if (this.measurementInterval) {
      clearInterval(this.measurementInterval);
      this.measurementInterval = null;
    }
    
    this.agentMetrics.clear();
    this.responseTimes.clear();
    this.taskStartTimes.clear();
  }
}