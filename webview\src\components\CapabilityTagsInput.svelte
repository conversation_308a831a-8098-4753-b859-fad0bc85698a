<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let selectedTags: string[] = [];
  export let suggestions: string[] = [];
  export let placeholder: string = 'Add capability...';
  export let maxTags: number = 20;
  export let allowCustom: boolean = true;
  export let disabled: boolean = false;
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let variant: 'default' | 'compact' | 'inline' = 'default';
  
  let inputValue = '';
  let inputElement: HTMLInputElement;
  let showSuggestions = false;
  let filteredSuggestions: string[] = [];
  let selectedSuggestionIndex = -1;
  
  // Predefined capability categories with suggestions
  const capabilityCategories = {
    'Technical Skills': [
      'code_generation', 'debugging', 'testing', 'refactoring', 'documentation',
      'api_design', 'database_design', 'performance_optimization', 'security_analysis',
      'code_review', 'architecture_design', 'deployment', 'monitoring'
    ],
    'Programming Languages': [
      'javascript', 'typescript', 'python', 'java', 'csharp', 'golang', 'rust',
      'php', 'ruby', 'swift', 'kotlin', 'cpp', 'scala', 'r', 'sql'
    ],
    'Frameworks & Libraries': [
      'react', 'vue', 'angular', 'nodejs', 'django', 'flask', 'spring',
      'laravel', 'rails', 'express', 'fastapi', 'nextjs', 'nuxtjs'
    ],
    'Development Tools': [
      'git', 'docker', 'kubernetes', 'jenkins', 'github_actions', 'terraform',
      'ansible', 'webpack', 'vite', 'eslint', 'prettier', 'jest', 'cypress'
    ],
    'Data & Analytics': [
      'data_analysis', 'machine_learning', 'data_visualization', 'statistics',
      'sql_optimization', 'etl', 'data_modeling', 'business_intelligence',
      'pandas', 'numpy', 'tensorflow', 'pytorch', 'scikit_learn'
    ],
    'Cloud & Infrastructure': [
      'aws', 'azure', 'gcp', 'serverless', 'microservices', 'load_balancing',
      'cdn', 'caching', 'monitoring', 'logging', 'backup', 'disaster_recovery'
    ],
    'Design & UX': [
      'ui_design', 'ux_research', 'prototyping', 'wireframing', 'user_testing',
      'accessibility', 'responsive_design', 'design_systems', 'figma', 'sketch'
    ],
    'Project Management': [
      'agile', 'scrum', 'kanban', 'stakeholder_management', 'requirements_gathering',
      'sprint_planning', 'team_coordination', 'risk_management', 'budget_planning'
    ],
    'Communication': [
      'technical_writing', 'presentation', 'mentoring', 'team_leadership',
      'cross_functional_collaboration', 'client_communication', 'documentation'
    ],
    'Quality Assurance': [
      'test_planning', 'test_automation', 'regression_testing', 'performance_testing',
      'security_testing', 'manual_testing', 'bug_tracking', 'quality_metrics'
    ]
  };
  
  // Flatten all suggestions
  const allSuggestions = Object.values(capabilityCategories).flat();
  
  // Combine provided suggestions with predefined ones
  $: combinedSuggestions = [...new Set([...suggestions, ...allSuggestions])];
  
  // Filter suggestions based on input
  $: {
    if (inputValue.trim()) {
      filteredSuggestions = combinedSuggestions
        .filter(tag => 
          tag.toLowerCase().includes(inputValue.toLowerCase()) &&
          !selectedTags.includes(tag)
        )
        .slice(0, 10);
      showSuggestions = filteredSuggestions.length > 0;
    } else {
      filteredSuggestions = [];
      showSuggestions = false;
    }
    selectedSuggestionIndex = -1;
  }
  
  // Size classes
  $: sizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  }[size];
  
  $: inputSizeClasses = {
    small: 'px-2 py-1 text-sm',
    medium: 'px-3 py-2 text-base',
    large: 'px-4 py-3 text-lg'
  }[size];
  
  $: tagSizeClasses = {
    small: 'px-2 py-1 text-xs',
    medium: 'px-3 py-1 text-sm',
    large: 'px-4 py-2 text-base'
  }[size];
  
  function addTag(tag: string) {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;
    
    if (selectedTags.length >= maxTags) {
      dispatch('maxTagsReached', { maxTags });
      return;
    }
    
    if (!selectedTags.includes(trimmedTag)) {
      selectedTags = [...selectedTags, trimmedTag];
      dispatch('tagsChanged', { tags: selectedTags });
      dispatch('tagAdded', { tag: trimmedTag });
    }
    
    inputValue = '';
    showSuggestions = false;
    inputElement?.focus();
  }
  
  function removeTag(tagToRemove: string) {
    selectedTags = selectedTags.filter(tag => tag !== tagToRemove);
    dispatch('tagsChanged', { tags: selectedTags });
    dispatch('tagRemoved', { tag: tagToRemove });
  }
  
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {
        addTag(filteredSuggestions[selectedSuggestionIndex]);
      } else if (allowCustom && inputValue.trim()) {
        addTag(inputValue);
      }
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      selectedSuggestionIndex = Math.min(selectedSuggestionIndex + 1, filteredSuggestions.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      selectedSuggestionIndex = Math.max(selectedSuggestionIndex - 1, -1);
    } else if (event.key === 'Escape') {
      showSuggestions = false;
      selectedSuggestionIndex = -1;
    } else if (event.key === 'Backspace' && !inputValue && selectedTags.length > 0) {
      removeTag(selectedTags[selectedTags.length - 1]);
    } else if (event.key === ',' || event.key === ';') {
      event.preventDefault();
      if (inputValue.trim()) {
        addTag(inputValue);
      }
    }
  }
  
  function handleInput() {
    showSuggestions = true;
  }
  
  function handleFocus() {
    if (inputValue.trim()) {
      showSuggestions = true;
    }
  }
  
  function handleBlur() {
    // Delay hiding suggestions to allow for click events
    setTimeout(() => {
      showSuggestions = false;
    }, 200);
  }
  
  function getTagColor(tag: string): string {
    // Generate consistent color based on tag content
    let hash = 0;
    for (let i = 0; i < tag.length; i++) {
      hash = tag.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const colors = [
      'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-red-600',
      'bg-yellow-600', 'bg-indigo-600', 'bg-pink-600', 'bg-teal-600'
    ];
    
    return colors[Math.abs(hash) % colors.length];
  }
  
  function getCategoryForTag(tag: string): string | null {
    for (const [category, tags] of Object.entries(capabilityCategories)) {
      if (tags.includes(tag)) {
        return category;
      }
    }
    return null;
  }
  
  function clearAllTags() {
    selectedTags = [];
    dispatch('tagsChanged', { tags: selectedTags });
    dispatch('allTagsCleared');
  }
  
  function addSuggestedCategory(category: string) {
    const categoryTags = capabilityCategories[category] || [];
    const newTags = categoryTags.filter(tag => !selectedTags.includes(tag));
    const tagsToAdd = newTags.slice(0, Math.min(5, maxTags - selectedTags.length));
    
    if (tagsToAdd.length > 0) {
      selectedTags = [...selectedTags, ...tagsToAdd];
      dispatch('tagsChanged', { tags: selectedTags });
      dispatch('categoryAdded', { category, tags: tagsToAdd });
    }
  }
</script>

<div class="capability-tags-input {variant} {sizeClasses}" class:disabled>
  {#if variant === 'default'}
    <!-- Default Layout -->
    <div class="tags-container">
      <!-- Selected Tags -->
      {#if selectedTags.length > 0}
        <div class="selected-tags">
          {#each selectedTags as tag (tag)}
            <span class="tag {tagSizeClasses} {getTagColor(tag)}">
              <span class="tag-text">{tag}</span>
              {#if !disabled}
                <button
                  type="button"
                  class="tag-remove"
                  on:click={() => removeTag(tag)}
                  title="Remove {tag}"
                >
                  ×
                </button>
              {/if}
            </span>
          {/each}
          
          {#if selectedTags.length > 0 && !disabled}
            <button
              type="button"
              class="clear-all-btn {tagSizeClasses}"
              on:click={clearAllTags}
              title="Clear all tags"
            >
              Clear all
            </button>
          {/if}
        </div>
      {/if}
      
      <!-- Input Container -->
      <div class="input-container">
        <input
          bind:this={inputElement}
          bind:value={inputValue}
          {placeholder}
          {disabled}
          class="tags-input {inputSizeClasses}"
          on:keydown={handleKeyDown}
          on:input={handleInput}
          on:focus={handleFocus}
          on:blur={handleBlur}
        />
        
        <div class="input-info">
          <span class="tag-count">{selectedTags.length}/{maxTags}</span>
          {#if allowCustom}
            <span class="custom-hint">Press Enter or comma to add</span>
          {/if}
        </div>
      </div>
      
      <!-- Suggestions Dropdown -->
      {#if showSuggestions && filteredSuggestions.length > 0}
        <div class="suggestions-dropdown">
          {#each filteredSuggestions as suggestion, index (suggestion)}
            <button
              type="button"
              class="suggestion-item {selectedSuggestionIndex === index ? 'selected' : ''}"
              on:click={() => addTag(suggestion)}
            >
              <span class="suggestion-text">{suggestion}</span>
              {#if getCategoryForTag(suggestion)}
                <span class="suggestion-category">{getCategoryForTag(suggestion)}</span>
              {/if}
            </button>
          {/each}
        </div>
      {/if}
    </div>
    
    <!-- Category Suggestions -->
    <div class="category-suggestions">
      <h4>Quick Add Categories:</h4>
      <div class="category-buttons">
        {#each Object.keys(capabilityCategories).slice(0, 6) as category}
          <button
            type="button"
            class="category-btn {tagSizeClasses}"
            on:click={() => addSuggestedCategory(category)}
            {disabled}
          >
            + {category}
          </button>
        {/each}
      </div>
    </div>
    
  {:else if variant === 'compact'}
    <!-- Compact Layout -->
    <div class="compact-container">
      <div class="compact-input">
        <input
          bind:this={inputElement}
          bind:value={inputValue}
          {placeholder}
          {disabled}
          class="tags-input {inputSizeClasses}"
          on:keydown={handleKeyDown}
          on:input={handleInput}
          on:focus={handleFocus}
          on:blur={handleBlur}
        />
        <span class="tag-count">{selectedTags.length}/{maxTags}</span>
      </div>
      
      {#if selectedTags.length > 0}
        <div class="compact-tags">
          {#each selectedTags as tag (tag)}
            <span class="tag compact {getTagColor(tag)}">
              {tag}
              {#if !disabled}
                <button class="tag-remove" on:click={() => removeTag(tag)}>×</button>
              {/if}
            </span>
          {/each}
        </div>
      {/if}
      
      {#if showSuggestions && filteredSuggestions.length > 0}
        <div class="suggestions-dropdown compact">
          {#each filteredSuggestions.slice(0, 5) as suggestion (suggestion)}
            <button class="suggestion-item" on:click={() => addTag(suggestion)}>
              {suggestion}
            </button>
          {/each}
        </div>
      {/if}
    </div>
    
  {:else if variant === 'inline'}
    <!-- Inline Layout -->
    <div class="inline-container">
      <div class="inline-tags">
        {#each selectedTags as tag (tag)}
          <span class="tag inline {getTagColor(tag)}">
            {tag}
            {#if !disabled}
              <button class="tag-remove" on:click={() => removeTag(tag)}>×</button>
            {/if}
          </span>
        {/each}
        
        <input
          bind:this={inputElement}
          bind:value={inputValue}
          {placeholder}
          {disabled}
          class="inline-input {inputSizeClasses}"
          on:keydown={handleKeyDown}
          on:input={handleInput}
          on:focus={handleFocus}
          on:blur={handleBlur}
        />
      </div>
      
      {#if showSuggestions && filteredSuggestions.length > 0}
        <div class="suggestions-dropdown inline">
          {#each filteredSuggestions.slice(0, 8) as suggestion (suggestion)}
            <button class="suggestion-item" on:click={() => addTag(suggestion)}>
              {suggestion}
            </button>
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .capability-tags-input {
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .capability-tags-input.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  /* Default Layout */
  .tags-container {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 12px;
  }
  
  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: white;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
  }
  
  .tag.compact {
    padding: 2px 8px;
    font-size: 12px;
    gap: 4px;
  }
  
  .tag.inline {
    padding: 4px 8px;
    font-size: 13px;
    margin-right: 4px;
  }
  
  .tag-text {
    white-space: nowrap;
  }
  
  .tag-remove {
    background: none;
    border: none;
    color: currentColor;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    padding: 0;
    opacity: 0.7;
    transition: opacity 0.2s;
  }
  
  .tag-remove:hover {
    opacity: 1;
  }
  
  .clear-all-btn {
    background: #374151;
    border: 1px solid #4b5563;
    color: #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .clear-all-btn:hover {
    background: #4b5563;
  }
  
  .input-container {
    position: relative;
  }
  
  .tags-input {
    width: 100%;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #ffffff;
    outline: none;
    transition: all 0.2s;
  }
  
  .tags-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .tags-input::placeholder {
    color: #9ca3af;
  }
  
  .input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 6px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  .tag-count {
    font-weight: 500;
  }
  
  .custom-hint {
    font-style: italic;
  }
  
  .suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 6px;
    margin-top: 4px;
    max-height: 200px;
    overflow-y: auto;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .suggestions-dropdown.compact {
    max-height: 150px;
  }
  
  .suggestions-dropdown.inline {
    max-height: 120px;
  }
  
  .suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    background: none;
    border: none;
    color: #ffffff;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .suggestion-item:hover,
  .suggestion-item.selected {
    background: #374151;
  }
  
  .suggestion-text {
    font-weight: 500;
  }
  
  .suggestion-category {
    font-size: 11px;
    color: #9ca3af;
    background: #374151;
    padding: 2px 6px;
    border-radius: 3px;
  }
  
  .category-suggestions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #374151;
  }
  
  .category-suggestions h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #d1d5db;
  }
  
  .category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .category-btn {
    background: #374151;
    border: 1px solid #4b5563;
    color: #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 12px;
  }
  
  .category-btn:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  /* Compact Layout */
  .compact-container {
    position: relative;
  }
  
  .compact-input {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 8px 12px;
  }
  
  .compact-input .tags-input {
    flex: 1;
    background: none;
    border: none;
    padding: 0;
  }
  
  .compact-input .tags-input:focus {
    box-shadow: none;
  }
  
  .compact-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
  }
  
  /* Inline Layout */
  .inline-container {
    position: relative;
  }
  
  .inline-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 8px;
    min-height: 40px;
  }
  
  .inline-input {
    flex: 1;
    min-width: 120px;
    background: none;
    border: none;
    color: #ffffff;
    outline: none;
  }
  
  .inline-input::placeholder {
    color: #9ca3af;
  }
  
  /* Color Classes */
  .bg-blue-600 { background-color: #2563eb; }
  .bg-green-600 { background-color: #16a34a; }
  .bg-purple-600 { background-color: #9333ea; }
  .bg-red-600 { background-color: #dc2626; }
  .bg-yellow-600 { background-color: #ca8a04; }
  .bg-indigo-600 { background-color: #4f46e5; }
  .bg-pink-600 { background-color: #db2777; }
  .bg-teal-600 { background-color: #0d9488; }
</style>