<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let value: string = '';
  export let placeholder: string = 'Enter your prompt...';
  export let maxLength: number = 10000;
  export let showLineNumbers: boolean = true;
  export let showSyntaxHelp: boolean = true;
  export let autoResize: boolean = true;
  export let disabled: boolean = false;
  export let theme: 'dark' | 'light' = 'dark';
  
  let textareaElement: HTMLTextAreaElement;
  let highlightElement: HTMLDivElement;
  let editorContainer: HTMLDivElement;
  
  // Syntax highlighting patterns
  const syntaxPatterns = [
    // Variables: {variable_name}
    { pattern: /\{([^}]+)\}/g, class: 'variable' },
    // Commands: [COMMAND]
    { pattern: /\[([A-Z_]+)\]/g, class: 'command' },
    // Emphasis: **bold** and *italic*
    { pattern: /\*\*([^*]+)\*\*/g, class: 'bold' },
    { pattern: /\*([^*]+)\*/g, class: 'italic' },
    // Code blocks: `code`
    { pattern: /`([^`]+)`/g, class: 'code' },
    // Special directives: @directive
    { pattern: /@(\w+)/g, class: 'directive' },
    // Placeholders: <placeholder>
    { pattern: /<([^>]+)>/g, class: 'placeholder' },
    // Numbers
    { pattern: /\b\d+\b/g, class: 'number' },
    // Quotes
    { pattern: /"([^"]+)"/g, class: 'string' },
    { pattern: /'([^']+)'/g, class: 'string' }
  ];
  
  // Prompt templates
  const promptTemplates = [
    {
      name: 'Basic Assistant',
      description: 'Simple assistant template',
      template: `You are a helpful AI assistant. Please respond to user queries with accurate and helpful information.

Key guidelines:
- Be concise but thorough
- Ask clarifying questions when needed
- Provide examples when helpful`
    },
    {
      name: 'Code Expert',
      description: 'Programming specialist template',
      template: `You are an expert programmer with deep knowledge of {language} and software development best practices.

Your capabilities include:
- **Code Generation**: Writing clean, efficient code
- **Code Review**: Identifying bugs and improvements
- **Architecture**: Designing scalable solutions
- **Testing**: Creating comprehensive test suites

Guidelines:
- Follow industry best practices
- Include error handling
- Write clear documentation
- Consider performance implications`
    },
    {
      name: 'Creative Writer',
      description: 'Content creation specialist',
      template: `You are a creative writing expert specializing in {genre} content.

Your expertise includes:
- *Storytelling*: Crafting engaging narratives
- *Character Development*: Creating compelling characters
- *Style*: Adapting tone and voice
- *Research*: Incorporating accurate details

@tone: {tone}
@audience: {target_audience}
@length: {word_count} words`
    },
    {
      name: 'Data Analyst',
      description: 'Data analysis and insights',
      template: `You are a data analyst expert with strong skills in statistics, data visualization, and business intelligence.

Core competencies:
- **Analysis**: Statistical analysis and pattern recognition
- **Visualization**: Creating clear charts and graphs
- **Insights**: Extracting actionable business insights
- **Tools**: Proficient in Python, R, SQL, and BI tools

Approach:
1. Understand the business context
2. Analyze data systematically
3. Validate findings
4. Present clear recommendations`
    }
  ];
  
  let currentTemplate = '';
  let showTemplates = false;
  let cursorPosition = 0;
  let selectedText = '';
  
  $: characterCount = value.length;
  $: lineCount = value.split('\n').length;
  $: wordsCount = value.trim() ? value.trim().split(/\s+/).length : 0;
  
  onMount(() => {
    updateHighlighting();
    if (autoResize) {
      adjustHeight();
    }
  });
  
  function updateHighlighting() {
    if (!highlightElement) return;
    
    let highlighted = value;
    
    // Apply syntax highlighting
    syntaxPatterns.forEach(({ pattern, class: className }) => {
      highlighted = highlighted.replace(pattern, `<span class="syntax-${className}">$&</span>`);
    });
    
    // Add line breaks
    highlighted = highlighted.replace(/\n/g, '<br>');
    
    // Add a space at the end to prevent cursor issues
    highlighted += '<br>';
    
    highlightElement.innerHTML = highlighted;
  }
  
  function handleInput(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    value = target.value;
    cursorPosition = target.selectionStart;
    selectedText = target.value.substring(target.selectionStart, target.selectionEnd);
    
    updateHighlighting();
    
    if (autoResize) {
      adjustHeight();
    }
    
    dispatch('input', { value, cursorPosition, selectedText });
  }
  
  function handleKeyDown(event: KeyboardEvent) {
    const target = event.target as HTMLTextAreaElement;
    
    // Tab key - insert spaces instead of losing focus
    if (event.key === 'Tab') {
      event.preventDefault();
      const start = target.selectionStart;
      const end = target.selectionEnd;
      
      value = value.substring(0, start) + '  ' + value.substring(end);
      
      // Set cursor position
      setTimeout(() => {
        target.selectionStart = target.selectionEnd = start + 2;
      }, 0);
    }
    
    // Ctrl+Enter or Cmd+Enter - dispatch complete event
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      dispatch('submit', { value });
    }
    
    // Auto-closing brackets and quotes
    if (event.key === '{') {
      insertAutoClose(target, '{', '}');
    } else if (event.key === '[') {
      insertAutoClose(target, '[', ']');
    } else if (event.key === '<') {
      insertAutoClose(target, '<', '>');
    } else if (event.key === '"') {
      insertAutoClose(target, '"', '"');
    } else if (event.key === "'") {
      insertAutoClose(target, "'", "'");
    }
  }
  
  function insertAutoClose(target: HTMLTextAreaElement, open: string, close: string) {
    const start = target.selectionStart;
    const end = target.selectionEnd;
    const selectedText = value.substring(start, end);
    
    // If text is selected, wrap it
    if (start !== end) {
      value = value.substring(0, start) + open + selectedText + close + value.substring(end);
      setTimeout(() => {
        target.selectionStart = start + 1;
        target.selectionEnd = start + 1 + selectedText.length;
      }, 0);
    }
  }
  
  function adjustHeight() {
    if (!textareaElement) return;
    
    textareaElement.style.height = 'auto';
    textareaElement.style.height = Math.max(120, textareaElement.scrollHeight) + 'px';
  }
  
  function insertTemplate(template: string) {
    const start = textareaElement.selectionStart;
    const end = textareaElement.selectionEnd;
    
    value = value.substring(0, start) + template + value.substring(end);
    showTemplates = false;
    
    setTimeout(() => {
      textareaElement.focus();
      textareaElement.selectionStart = textareaElement.selectionEnd = start + template.length;
      updateHighlighting();
      if (autoResize) adjustHeight();
    }, 0);
    
    dispatch('templateInserted', { template });
  }
  
  function insertSnippet(snippet: string) {
    const start = textareaElement.selectionStart;
    const end = textareaElement.selectionEnd;
    
    value = value.substring(0, start) + snippet + value.substring(end);
    
    setTimeout(() => {
      textareaElement.focus();
      textareaElement.selectionStart = textareaElement.selectionEnd = start + snippet.length;
      updateHighlighting();
      if (autoResize) adjustHeight();
    }, 0);
  }
  
  function formatSelection(format: string) {
    const start = textareaElement.selectionStart;
    const end = textareaElement.selectionEnd;
    const selectedText = value.substring(start, end);
    
    if (!selectedText) return;
    
    let formatted = '';
    switch (format) {
      case 'bold':
        formatted = `**${selectedText}**`;
        break;
      case 'italic':
        formatted = `*${selectedText}*`;
        break;
      case 'code':
        formatted = `\`${selectedText}\``;
        break;
      case 'variable':
        formatted = `{${selectedText}}`;
        break;
      case 'command':
        formatted = `[${selectedText.toUpperCase()}]`;
        break;
      case 'placeholder':
        formatted = `<${selectedText}>`;
        break;
    }
    
    if (formatted) {
      value = value.substring(0, start) + formatted + value.substring(end);
      setTimeout(() => {
        textareaElement.focus();
        textareaElement.selectionStart = start;
        textareaElement.selectionEnd = start + formatted.length;
        updateHighlighting();
      }, 0);
    }
  }
  
  // Common snippets
  const snippets = [
    { name: 'Variable', snippet: '{variable_name}' },
    { name: 'Command', snippet: '[COMMAND]' },
    { name: 'Placeholder', snippet: '<placeholder>' },
    { name: 'Directive', snippet: '@directive' },
    { name: 'Code Block', snippet: '`code`' },
    { name: 'Bold Text', snippet: '**bold text**' },
    { name: 'Italic Text', snippet: '*italic text*' }
  ];
</script>

<div class="prompt-editor {theme}" bind:this={editorContainer}>
  <!-- Toolbar -->
  <div class="toolbar">
    <div class="toolbar-left">
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => showTemplates = !showTemplates}
        title="Insert Template"
      >
        📄 Templates
      </button>
      
      <div class="toolbar-divider"></div>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('bold')}
        title="Bold (Ctrl+B)"
      >
        <strong>B</strong>
      </button>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('italic')}
        title="Italic (Ctrl+I)"
      >
        <em>I</em>
      </button>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('code')}
        title="Code"
      >
        &lt;/&gt;
      </button>
      
      <div class="toolbar-divider"></div>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('variable')}
        title="Variable"
      >
        {x}
      </button>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('command')}
        title="Command"
      >
        [CMD]
      </button>
      
      <button
        type="button"
        class="toolbar-btn"
        on:click={() => formatSelection('placeholder')}
        title="Placeholder"
      >
        &lt;...&gt;
      </button>
    </div>
    
    <div class="toolbar-right">
      <span class="word-count">{wordsCount} words</span>
      <span class="char-count">{characterCount}/{maxLength}</span>
    </div>
  </div>
  
  <!-- Templates Dropdown -->
  {#if showTemplates}
    <div class="templates-dropdown">
      <h4>Prompt Templates</h4>
      <div class="template-list">
        {#each promptTemplates as template}
          <button
            type="button"
            class="template-item"
            on:click={() => insertTemplate(template.template)}
          >
            <div class="template-name">{template.name}</div>
            <div class="template-description">{template.description}</div>
          </button>
        {/each}
      </div>
    </div>
  {/if}
  
  <!-- Editor Container -->
  <div class="editor-container">
    {#if showLineNumbers}
      <div class="line-numbers">
        {#each Array(lineCount) as _, i}
          <div class="line-number">{i + 1}</div>
        {/each}
      </div>
    {/if}
    
    <div class="editor-content">
      <!-- Syntax Highlighting Layer -->
      <div
        class="highlight-layer"
        bind:this={highlightElement}
        aria-hidden="true"
      ></div>
      
      <!-- Input Textarea -->
      <textarea
        bind:this={textareaElement}
        bind:value={value}
        {placeholder}
        {disabled}
        class="editor-textarea"
        spellcheck="false"
        on:input={handleInput}
        on:keydown={handleKeyDown}
        on:scroll={() => {
          if (highlightElement) {
            highlightElement.scrollTop = textareaElement.scrollTop;
            highlightElement.scrollLeft = textareaElement.scrollLeft;
          }
        }}
      ></textarea>
    </div>
  </div>
  
  <!-- Syntax Help -->
  {#if showSyntaxHelp}
    <div class="syntax-help">
      <h5>Syntax Highlighting</h5>
      <div class="syntax-examples">
        <div class="syntax-group">
          <strong>Variables:</strong>
          {#each snippets.slice(0, 4) as snippet}
            <button
              type="button"
              class="syntax-snippet"
              on:click={() => insertSnippet(snippet.snippet)}
            >
              {snippet.snippet}
            </button>
          {/each}
        </div>
        <div class="syntax-group">
          <strong>Formatting:</strong>
          {#each snippets.slice(4) as snippet}
            <button
              type="button"
              class="syntax-snippet"
              on:click={() => insertSnippet(snippet.snippet)}
            >
              {snippet.snippet}
            </button>
          {/each}
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .prompt-editor {
    border: 1px solid #374151;
    border-radius: 8px;
    background: #1f2937;
    color: #ffffff;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  }
  
  .prompt-editor.light {
    background: #ffffff;
    color: #000000;
    border-color: #d1d5db;
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #374151;
    background: #111827;
  }
  
  .prompt-editor.light .toolbar {
    background: #f9fafb;
    border-bottom-color: #d1d5db;
  }
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  .toolbar-btn {
    padding: 4px 8px;
    background: transparent;
    border: 1px solid #374151;
    border-radius: 4px;
    color: #d1d5db;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .toolbar-btn:hover {
    background: #374151;
    border-color: #4b5563;
  }
  
  .prompt-editor.light .toolbar-btn {
    border-color: #d1d5db;
    color: #374151;
  }
  
  .prompt-editor.light .toolbar-btn:hover {
    background: #f3f4f6;
  }
  
  .toolbar-divider {
    width: 1px;
    height: 16px;
    background: #374151;
    margin: 0 4px;
  }
  
  .word-count, .char-count {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .templates-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: #1f2937;
    border: 1px solid #374151;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .templates-dropdown h4 {
    padding: 12px;
    margin: 0;
    font-size: 14px;
    border-bottom: 1px solid #374151;
  }
  
  .template-list {
    padding: 8px;
  }
  
  .template-item {
    display: block;
    width: 100%;
    padding: 8px;
    text-align: left;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    margin-bottom: 4px;
    transition: all 0.2s;
  }
  
  .template-item:hover {
    background: #374151;
    border-color: #4b5563;
  }
  
  .template-name {
    font-weight: 600;
    margin-bottom: 2px;
  }
  
  .template-description {
    font-size: 12px;
    color: #9ca3af;
  }
  
  .editor-container {
    display: flex;
    position: relative;
    min-height: 120px;
  }
  
  .line-numbers {
    padding: 12px 8px;
    background: #111827;
    border-right: 1px solid #374151;
    font-size: 12px;
    color: #6b7280;
    user-select: none;
    min-width: 40px;
  }
  
  .prompt-editor.light .line-numbers {
    background: #f9fafb;
    border-right-color: #d1d5db;
  }
  
  .line-number {
    height: 20px;
    line-height: 20px;
    text-align: right;
  }
  
  .editor-content {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  
  .highlight-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 12px;
    font-size: 14px;
    line-height: 20px;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: transparent;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
  }
  
  .editor-textarea {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    padding: 12px;
    background: transparent;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 20px;
    color: #ffffff;
    white-space: pre-wrap;
    word-wrap: break-word;
    z-index: 2;
  }
  
  .prompt-editor.light .editor-textarea {
    color: #000000;
  }
  
  .editor-textarea::placeholder {
    color: #6b7280;
  }
  
  /* Syntax Highlighting Styles */
  :global(.syntax-variable) {
    color: #fbbf24;
    font-weight: 600;
  }
  
  :global(.syntax-command) {
    color: #60a5fa;
    font-weight: 600;
  }
  
  :global(.syntax-bold) {
    color: #f87171;
    font-weight: bold;
  }
  
  :global(.syntax-italic) {
    color: #a78bfa;
    font-style: italic;
  }
  
  :global(.syntax-code) {
    color: #34d399;
    background: rgba(52, 211, 153, 0.1);
    padding: 1px 3px;
    border-radius: 2px;
  }
  
  :global(.syntax-directive) {
    color: #fb7185;
    font-weight: 600;
  }
  
  :global(.syntax-placeholder) {
    color: #fbbf24;
    font-style: italic;
  }
  
  :global(.syntax-number) {
    color: #60a5fa;
  }
  
  :global(.syntax-string) {
    color: #34d399;
  }
  
  .syntax-help {
    padding: 12px;
    border-top: 1px solid #374151;
    background: #111827;
  }
  
  .prompt-editor.light .syntax-help {
    background: #f9fafb;
    border-top-color: #d1d5db;
  }
  
  .syntax-help h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
  }
  
  .syntax-examples {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .syntax-group {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
  }
  
  .syntax-group strong {
    color: #d1d5db;
    white-space: nowrap;
  }
  
  .syntax-snippet {
    padding: 2px 6px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 3px;
    font-family: inherit;
    font-size: 11px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .syntax-snippet:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .prompt-editor.light .syntax-snippet {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
  }
  
  .prompt-editor.light .syntax-snippet:hover {
    background: #e5e7eb;
  }
</style>