"""
API routes for Metamorphic Reactor AutoGen service
Handles agent orchestration, task management, and real-time communication
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import asyncio
import json
import uuid
from datetime import datetime

from agents.orchestrator import AgentOrchestrator
from agents.agent_types import AgentRole, TaskStatus, AgentCreationRequest, AgentProfile
from utils.logging_config import get_logger

logger = get_logger(__name__)

# API Models
class TaskRequest(BaseModel):
    task_description: str = Field(..., min_length=1, max_length=10000)
    context_files: List[str] = Field(default_factory=list)
    max_rounds: int = Field(default=10, ge=1, le=50)
    timeout_minutes: int = Field(default=15, ge=1, le=60)
    require_consensus: bool = Field(default=True)
    agent_config: Dict[str, Any] = Field(default_factory=dict)

class AgentCreateRequest(BaseModel):
    agent_id: str = Field(..., min_length=1)
    role: AgentRole
    task_description: str
    context_files: List[str] = Field(default_factory=list)

class ConversationStartRequest(BaseModel):
    message: str = Field(..., min_length=1)
    context: Dict[str, Any] = Field(default_factory=dict)

class AgentResponse(BaseModel):
    agent_id: str
    session_id: str
    role: AgentRole
    status: TaskStatus
    created_at: datetime

class TaskResponse(BaseModel):
    task_id: str
    status: TaskStatus
    agents: List[AgentResponse]
    consensus_score: float
    created_at: datetime
    updated_at: datetime

class ConversationResponse(BaseModel):
    session_id: str
    message_id: str
    status: str
    token_count: int

# Router instances
health_router = APIRouter()
task_router = APIRouter()
agent_router = APIRouter()

# Global orchestrator reference (set by main.py)
orchestrator: AgentOrchestrator = None

def set_orchestrator(orch: AgentOrchestrator):
    """Set the global orchestrator instance"""
    global orchestrator
    orchestrator = orch

# Health endpoints
@health_router.get("/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "service": "metamorphic-reactor",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@health_router.get("/detailed")
async def detailed_health():
    """Detailed health check with service status"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        health_status = await orchestrator.get_health_status()
        return {
            "status": "healthy",
            "service": "metamorphic-reactor",
            "version": "1.0.0",
            "orchestrator": health_status,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

# Task management endpoints
@task_router.post("/create", response_model=TaskResponse)
async def create_task(request: TaskRequest, background_tasks: BackgroundTasks):
    """Create a new multi-agent task"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        task_id = str(uuid.uuid4())
        logger.info(f"Creating task {task_id}: {request.task_description[:100]}...")
        
        # Create task in orchestrator
        task_result = await orchestrator.create_task(
            task_id=task_id,
            description=request.task_description,
            context_files=request.context_files,
            max_rounds=request.max_rounds,
            timeout_minutes=request.timeout_minutes,
            require_consensus=request.require_consensus,
            agent_config=request.agent_config
        )
        
        # Start task execution in background
        background_tasks.add_task(orchestrator.execute_task, task_id)
        
        return TaskResponse(
            task_id=task_id,
            status=task_result["status"],
            agents=task_result["agents"],
            consensus_score=0.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")

@task_router.get("/{task_id}/status", response_model=TaskResponse)
async def get_task_status(task_id: str):
    """Get current status of a task"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        task_status = await orchestrator.get_task_status(task_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return TaskResponse(**task_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")

@task_router.post("/{task_id}/stop")
async def stop_task(task_id: str):
    """Stop a running task"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        result = await orchestrator.stop_task(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return {"message": f"Task {task_id} stopped successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop task: {str(e)}")

@task_router.get("/{task_id}/stream")
async def stream_task_tokens(task_id: str):
    """Stream real-time tokens from task execution"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    async def token_generator():
        try:
            async for token_data in orchestrator.stream_task_tokens(task_id):
                yield f"data: {json.dumps(token_data)}\n\n"
        except Exception as e:
            logger.error(f"Error streaming tokens: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return StreamingResponse(
        token_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*"
        }
    )

# Agent management endpoints
@agent_router.post("/create", response_model=AgentProfile)
async def create_agent_profile(request: AgentCreationRequest):
    """Create a new agent profile using the Agent Creation System"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        agent_id = str(uuid.uuid4())
        logger.info(f"Creating agent '{request.name}' with role {request.role}")
        
        # Create agent profile through orchestrator
        agent_profile = await orchestrator.create_agent_profile(
            agent_id=agent_id,
            name=request.name,
            role=request.role,
            model=request.model,
            prompt=request.prompt,
            capabilities=request.capabilities,
            team_id=request.team_id,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            max_retries=request.max_retries,
            timeout_seconds=request.timeout_seconds,
            consensus_threshold=request.consensus_threshold
        )
        
        return agent_profile
        
    except Exception as e:
        logger.error(f"Error creating agent profile: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create agent profile: {str(e)}")

@agent_router.post("/legacy/create", response_model=AgentResponse)
async def create_agent(request: AgentCreateRequest):
    """Create a new agent instance (legacy endpoint)"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        session_id = str(uuid.uuid4())
        logger.info(f"Creating agent {request.agent_id} with role {request.role}")
        
        agent_result = await orchestrator.create_agent(
            agent_id=request.agent_id,
            session_id=session_id,
            role=request.role,
            task_description=request.task_description,
            context_files=request.context_files
        )
        
        return AgentResponse(
            agent_id=request.agent_id,
            session_id=session_id,
            role=request.role,
            status=agent_result["status"],
            created_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Error creating agent: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create agent: {str(e)}")

@agent_router.post("/{session_id}/start", response_model=ConversationResponse)
async def start_conversation(session_id: str, request: ConversationStartRequest):
    """Start a conversation with an agent"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        message_id = str(uuid.uuid4())
        logger.info(f"Starting conversation {session_id}: {request.message[:100]}...")
        
        result = await orchestrator.start_conversation(
            session_id=session_id,
            message_id=message_id,
            message=request.message,
            context=request.context
        )
        
        return ConversationResponse(
            session_id=session_id,
            message_id=message_id,
            status=result["status"],
            token_count=result.get("token_count", 0)
        )
        
    except Exception as e:
        logger.error(f"Error starting conversation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start conversation: {str(e)}")

@agent_router.get("/{session_id}/status")
async def get_agent_status(session_id: str):
    """Get current status of an agent session"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        status = await orchestrator.get_agent_status(session_id)
        if not status:
            raise HTTPException(status_code=404, detail="Agent session not found")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent status: {str(e)}")

@agent_router.delete("/{agent_id}")
async def delete_agent_profile(agent_id: str):
    """Delete an agent profile"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        result = await orchestrator.delete_agent_profile(agent_id)
        if not result:
            raise HTTPException(status_code=404, detail="Agent profile not found")
        
        logger.info(f"Deleted agent profile: {agent_id}")
        return {"message": f"Agent profile {agent_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting agent profile: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete agent profile: {str(e)}")

@agent_router.delete("/session/{session_id}")
async def delete_agent_session(session_id: str):
    """Delete an agent session (legacy endpoint)"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        result = await orchestrator.delete_agent(session_id)
        if not result:
            raise HTTPException(status_code=404, detail="Agent session not found")
        
        return {"message": f"Agent session {session_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting agent session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete agent session: {str(e)}")

# Consensus and monitoring endpoints
@task_router.get("/{task_id}/consensus")
async def get_consensus_status(task_id: str):
    """Get consensus status for a task"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        consensus = await orchestrator.get_consensus_status(task_id)
        if consensus is None:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return consensus
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting consensus status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get consensus status: {str(e)}")

@task_router.get("/")
async def list_active_tasks():
    """List all active tasks"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        tasks = await orchestrator.list_active_tasks()
        return {"tasks": tasks, "count": len(tasks)}
        
    except Exception as e:
        logger.error(f"Error listing tasks: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list tasks: {str(e)}")

@agent_router.get("/")
async def list_agent_profiles():
    """List all agent profiles"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        profiles = await orchestrator.list_agent_profiles()
        return {"profiles": profiles, "count": len(profiles)}
        
    except Exception as e:
        logger.error(f"Error listing agent profiles: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list agent profiles: {str(e)}")

@agent_router.get("/sessions")
async def list_active_agent_sessions():
    """List all active agent sessions"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        agents = await orchestrator.list_active_agents()
        return {"agents": agents, "count": len(agents)}
        
    except Exception as e:
        logger.error(f"Error listing agent sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list agent sessions: {str(e)}")

@agent_router.get("/{agent_id}")
async def get_agent_profile(agent_id: str):
    """Get agent profile by ID"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        profile = await orchestrator.get_agent_profile(agent_id)
        if not profile:
            raise HTTPException(status_code=404, detail="Agent profile not found")
        
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent profile: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent profile: {str(e)}")

@agent_router.put("/{agent_id}")
async def update_agent_profile(agent_id: str, updates: Dict[str, Any]):
    """Update agent profile"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        profile = await orchestrator.update_agent_profile(agent_id, updates)
        if not profile:
            raise HTTPException(status_code=404, detail="Agent profile not found")
        
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating agent profile: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update agent profile: {str(e)}")

@agent_router.get("/roles/{role}")
async def get_agents_by_role(role: str):
    """Get agent profiles by role"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        # Convert string to AgentRole enum
        agent_role = AgentRole(role)
        profiles = await orchestrator.get_profiles_by_role(agent_role)
        return {"profiles": profiles, "count": len(profiles), "role": role}
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid role: {role}")
    except Exception as e:
        logger.error(f"Error getting agents by role: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agents by role: {str(e)}")

@agent_router.get("/teams/{team_id}")
async def get_agents_by_team(team_id: str):
    """Get agent profiles by team"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        profiles = await orchestrator.get_profiles_by_team(team_id)
        return {"profiles": profiles, "count": len(profiles), "team_id": team_id}
        
    except Exception as e:
        logger.error(f"Error getting agents by team: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agents by team: {str(e)}")

@agent_router.post("/validate")
async def validate_agent_creation(request: AgentCreationRequest):
    """Validate agent creation request"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        validation = await orchestrator.validate_agent_creation(request)
        return validation
        
    except Exception as e:
        logger.error(f"Error validating agent creation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to validate agent creation: {str(e)}")

@agent_router.get("/meta/models")
async def get_supported_models():
    """Get supported models by provider"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        models = await orchestrator.get_supported_models()
        return {"models": models}
        
    except Exception as e:
        logger.error(f"Error getting supported models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get supported models: {str(e)}")

@agent_router.get("/meta/roles")
async def get_supported_roles():
    """Get supported agent roles"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        roles = await orchestrator.get_supported_roles()
        return {"roles": roles}
        
    except Exception as e:
        logger.error(f"Error getting supported roles: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get supported roles: {str(e)}")

@agent_router.post("/{agent_id}/clone")
async def clone_agent_profile(agent_id: str, request: Dict[str, Any]):
    """Clone an existing agent profile"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        new_name = request.get("name")
        cloned_profile = await orchestrator.clone_agent_profile(agent_id, new_name)
        if not cloned_profile:
            raise HTTPException(status_code=404, detail="Agent profile not found")
        
        return cloned_profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cloning agent profile: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clone agent profile: {str(e)}")

@agent_router.post("/batch/clone")
async def batch_clone_agents(request: Dict[str, Any]):
    """Clone multiple agent profiles"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        agent_ids = request.get("agent_ids", [])
        name_prefix = request.get("name_prefix")
        
        if not agent_ids:
            raise HTTPException(status_code=400, detail="agent_ids is required")
        
        cloned_profiles = await orchestrator.batch_clone_agents(agent_ids, name_prefix)
        return {"profiles": cloned_profiles, "count": len(cloned_profiles)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error batch cloning agents: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to batch clone agents: {str(e)}")

@agent_router.post("/{agent_id}/template")
async def create_agent_template(agent_id: str, request: Dict[str, Any]):
    """Create a template from an existing agent profile"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        template_name = request.get("template_name")
        if not template_name:
            raise HTTPException(status_code=400, detail="template_name is required")
        
        template = await orchestrator.create_agent_template(agent_id, template_name)
        if not template:
            raise HTTPException(status_code=404, detail="Agent profile not found")
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating agent template: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create agent template: {str(e)}")

@agent_router.post("/template/create")
async def create_agent_from_template(request: Dict[str, Any]):
    """Create a new agent profile from a template"""
    if not orchestrator:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    
    try:
        template = request.get("template")
        name = request.get("name")
        
        if not template:
            raise HTTPException(status_code=400, detail="template is required")
        if not name:
            raise HTTPException(status_code=400, detail="name is required")
        
        profile = await orchestrator.create_agent_from_template(template, name)
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating agent from template: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create agent from template: {str(e)}")