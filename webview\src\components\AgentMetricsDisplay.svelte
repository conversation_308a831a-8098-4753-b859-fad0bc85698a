<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agent: any;
  export let showDetailedMetrics: boolean = false;
  export let showHistoricalData: boolean = false;
  export let refreshInterval: number = 30000; // 30 seconds
  export let compactView: boolean = false;
  
  interface MetricData {
    timestamp: string;
    value: number;
    unit?: string;
    trend?: 'up' | 'down' | 'stable';
    category: 'performance' | 'usage' | 'health' | 'resource';
  }
  
  interface PerformanceMetrics {
    responseTime: MetricData;
    successRate: MetricData;
    errorRate: MetricData;
    throughput: MetricData;
    uptime: MetricData;
  }
  
  interface UsageMetrics {
    totalRequests: MetricData;
    activeUsers: MetricData;
    requestsPerMinute: MetricData;
    peakUsage: MetricData;
    averageSessionTime: MetricData;
  }
  
  interface HealthMetrics {
    cpuUsage: MetricData;
    memoryUsage: MetricData;
    networkLatency: MetricData;
    diskUsage: MetricData;
    healthScore: MetricData;
  }
  
  let performanceMetrics: PerformanceMetrics = {
    responseTime: { timestamp: '', value: 0, unit: 'ms', trend: 'stable', category: 'performance' },
    successRate: { timestamp: '', value: 0, unit: '%', trend: 'stable', category: 'performance' },
    errorRate: { timestamp: '', value: 0, unit: '%', trend: 'stable', category: 'performance' },
    throughput: { timestamp: '', value: 0, unit: 'req/s', trend: 'stable', category: 'performance' },
    uptime: { timestamp: '', value: 0, unit: '%', trend: 'stable', category: 'performance' }
  };
  
  let usageMetrics: UsageMetrics = {
    totalRequests: { timestamp: '', value: 0, unit: '', trend: 'stable', category: 'usage' },
    activeUsers: { timestamp: '', value: 0, unit: '', trend: 'stable', category: 'usage' },
    requestsPerMinute: { timestamp: '', value: 0, unit: 'req/min', trend: 'stable', category: 'usage' },
    peakUsage: { timestamp: '', value: 0, unit: 'req/min', trend: 'stable', category: 'usage' },
    averageSessionTime: { timestamp: '', value: 0, unit: 'min', trend: 'stable', category: 'usage' }
  };
  
  let healthMetrics: HealthMetrics = {
    cpuUsage: { timestamp: '', value: 0, unit: '%', trend: 'stable', category: 'health' },
    memoryUsage: { timestamp: '', value: 0, unit: 'MB', trend: 'stable', category: 'health' },
    networkLatency: { timestamp: '', value: 0, unit: 'ms', trend: 'stable', category: 'health' },
    diskUsage: { timestamp: '', value: 0, unit: 'MB', trend: 'stable', category: 'health' },
    healthScore: { timestamp: '', value: 0, unit: '/100', trend: 'stable', category: 'health' }
  };
  
  let historicalData: any[] = [];
  let selectedTimeRange = '1h';
  let selectedMetricCategory = 'performance';
  let isLoading = false;
  let lastUpdated = new Date();
  let refreshTimer: NodeJS.Timeout | null = null;
  
  const timeRanges = [
    { value: '1h', label: '1 Hour' },
    { value: '6h', label: '6 Hours' },
    { value: '24h', label: '24 Hours' },
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' }
  ];
  
  const metricCategories = [
    { value: 'performance', label: 'Performance', icon: '⚡' },
    { value: 'usage', label: 'Usage', icon: '📊' },
    { value: 'health', label: 'Health', icon: '💓' },
    { value: 'all', label: 'All Metrics', icon: '🔍' }
  ];
  
  onMount(() => {
    updateMetrics();
    startAutoRefresh();
    
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  });
  
  function startAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
    
    refreshTimer = setInterval(() => {
      updateMetrics();
    }, refreshInterval);
  }
  
  async function updateMetrics() {
    isLoading = true;
    
    try {
      // Simulate API call to get metrics
      const metricsData = await fetchAgentMetrics(agent.id);
      
      // Update performance metrics
      performanceMetrics = {
        responseTime: createMetricData(metricsData.avgResponseTime || 0, 'ms', 'performance'),
        successRate: createMetricData(metricsData.successRate || 0, '%', 'performance'),
        errorRate: createMetricData(metricsData.errorRate || 0, '%', 'performance'),
        throughput: createMetricData(metricsData.throughput || 0, 'req/s', 'performance'),
        uptime: createMetricData(metricsData.uptime || 0, '%', 'performance')
      };
      
      // Update usage metrics
      usageMetrics = {
        totalRequests: createMetricData(metricsData.totalRequests || 0, '', 'usage'),
        activeUsers: createMetricData(metricsData.activeUsers || 0, '', 'usage'),
        requestsPerMinute: createMetricData(metricsData.requestsPerMinute || 0, 'req/min', 'usage'),
        peakUsage: createMetricData(metricsData.peakUsage || 0, 'req/min', 'usage'),
        averageSessionTime: createMetricData(metricsData.averageSessionTime || 0, 'min', 'usage')
      };
      
      // Update health metrics
      healthMetrics = {
        cpuUsage: createMetricData(metricsData.cpuUsage || 0, '%', 'health'),
        memoryUsage: createMetricData(metricsData.memoryUsage || 0, 'MB', 'health'),
        networkLatency: createMetricData(metricsData.networkLatency || 0, 'ms', 'health'),
        diskUsage: createMetricData(metricsData.diskUsage || 0, 'MB', 'health'),
        healthScore: createMetricData(metricsData.healthScore || 0, '/100', 'health')
      };
      
      lastUpdated = new Date();
      
      // Dispatch updated metrics
      dispatch('metricsUpdated', {
        agent,
        performance: performanceMetrics,
        usage: usageMetrics,
        health: healthMetrics
      });
      
    } catch (error) {
      console.error('Error updating metrics:', error);
      dispatch('metricsError', { error, agent });
    } finally {
      isLoading = false;
    }
  }
  
  function createMetricData(value: number, unit: string, category: string): MetricData {
    return {
      timestamp: new Date().toISOString(),
      value,
      unit,
      trend: calculateTrend(value),
      category: category as 'performance' | 'usage' | 'health' | 'resource'
    };
  }
  
  function calculateTrend(currentValue: number): 'up' | 'down' | 'stable' {
    // Simple trend calculation - in real implementation, compare with previous values
    const random = Math.random();
    return random > 0.6 ? 'up' : random < 0.4 ? 'down' : 'stable';
  }
  
  async function fetchAgentMetrics(agentId: string) {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      avgResponseTime: 50 + Math.random() * 200,
      successRate: 85 + Math.random() * 15,
      errorRate: Math.random() * 5,
      throughput: 10 + Math.random() * 50,
      uptime: 95 + Math.random() * 5,
      totalRequests: Math.floor(1000 + Math.random() * 5000),
      activeUsers: Math.floor(Math.random() * 100),
      requestsPerMinute: 5 + Math.random() * 25,
      peakUsage: 20 + Math.random() * 80,
      averageSessionTime: 2 + Math.random() * 10,
      cpuUsage: Math.random() * 80,
      memoryUsage: 100 + Math.random() * 400,
      networkLatency: 10 + Math.random() * 50,
      diskUsage: 500 + Math.random() * 1000,
      healthScore: 70 + Math.random() * 30
    };
  }
  
  async function loadHistoricalData() {
    if (!showHistoricalData) return;
    
    isLoading = true;
    try {
      // Simulate loading historical data
      const data = await fetchHistoricalData(agent.id, selectedTimeRange, selectedMetricCategory);
      historicalData = data;
    } catch (error) {
      console.error('Error loading historical data:', error);
    } finally {
      isLoading = false;
    }
  }
  
  async function fetchHistoricalData(agentId: string, timeRange: string, category: string) {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const dataPoints = timeRange === '1h' ? 60 : timeRange === '6h' ? 360 : 720;
    return Array.from({ length: dataPoints }, (_, i) => ({
      timestamp: new Date(Date.now() - i * 60000).toISOString(),
      value: Math.random() * 100,
      category
    }));
  }
  
  function getMetricColor(value: number, type: string): string {
    if (type === 'successRate' || type === 'uptime' || type === 'healthScore') {
      return value >= 90 ? '#10b981' : value >= 70 ? '#f59e0b' : '#ef4444';
    } else if (type === 'errorRate' || type === 'cpuUsage' || type === 'memoryUsage') {
      return value <= 10 ? '#10b981' : value <= 30 ? '#f59e0b' : '#ef4444';
    } else if (type === 'responseTime' || type === 'networkLatency') {
      return value <= 100 ? '#10b981' : value <= 300 ? '#f59e0b' : '#ef4444';
    }
    return '#3b82f6';
  }
  
  function getTrendIcon(trend: string): string {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      default: return '➡️';
    }
  }
  
  function formatValue(value: number, unit: string): string {
    if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else if (unit === 'ms') {
      return `${value.toFixed(0)}ms`;
    } else if (unit === 'MB') {
      return `${value.toFixed(0)}MB`;
    } else if (unit === 'req/s' || unit === 'req/min') {
      return `${value.toFixed(1)}${unit}`;
    } else if (unit === 'min') {
      return `${value.toFixed(1)}min`;
    }
    return value.toFixed(0) + unit;
  }
  
  function exportMetrics() {
    const exportData = {
      agent: {
        id: agent.id,
        name: agent.name,
        role: agent.role
      },
      performance: performanceMetrics,
      usage: usageMetrics,
      health: healthMetrics,
      historicalData: showHistoricalData ? historicalData : null,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${agent.name}-metrics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
  
  $: if (selectedTimeRange || selectedMetricCategory) {
    loadHistoricalData();
  }
</script>

<div class="metrics-display" class:compact={compactView}>
  <!-- Header -->
  <div class="metrics-header">
    <div class="header-title">
      <h3>Performance Metrics</h3>
      <div class="last-updated">
        Last updated: {lastUpdated.toLocaleTimeString()}
        {#if isLoading}
          <span class="loading-indicator">🔄</span>
        {/if}
      </div>
    </div>
    
    <div class="header-actions">
      <button class="action-btn" on:click={updateMetrics} disabled={isLoading}>
        🔄 Refresh
      </button>
      <button class="action-btn" on:click={exportMetrics}>
        📤 Export
      </button>
      <button class="action-btn" on:click={() => showDetailedMetrics = !showDetailedMetrics}>
        {showDetailedMetrics ? '📊' : '📈'} {showDetailedMetrics ? 'Simple' : 'Detailed'}
      </button>
    </div>
  </div>
  
  <!-- Performance Metrics -->
  <div class="metrics-section">
    <div class="section-title">
      <span class="section-icon">⚡</span>
      <h4>Performance</h4>
    </div>
    
    <div class="metrics-grid" class:compact={compactView}>
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Response Time</span>
          <span class="metric-trend">{getTrendIcon(performanceMetrics.responseTime.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(performanceMetrics.responseTime.value, 'responseTime')}">
          {formatValue(performanceMetrics.responseTime.value, performanceMetrics.responseTime.unit)}
        </div>
        {#if showDetailedMetrics}
          <div class="metric-details">
            <div class="detail-item">
              <span class="detail-label">Target:</span>
              <span class="detail-value">&lt; 200ms</span>
            </div>
          </div>
        {/if}
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Success Rate</span>
          <span class="metric-trend">{getTrendIcon(performanceMetrics.successRate.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(performanceMetrics.successRate.value, 'successRate')}">
          {formatValue(performanceMetrics.successRate.value, performanceMetrics.successRate.unit)}
        </div>
        {#if showDetailedMetrics}
          <div class="metric-details">
            <div class="detail-item">
              <span class="detail-label">Target:</span>
              <span class="detail-value">&gt; 95%</span>
            </div>
          </div>
        {/if}
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Error Rate</span>
          <span class="metric-trend">{getTrendIcon(performanceMetrics.errorRate.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(performanceMetrics.errorRate.value, 'errorRate')}">
          {formatValue(performanceMetrics.errorRate.value, performanceMetrics.errorRate.unit)}
        </div>
        {#if showDetailedMetrics}
          <div class="metric-details">
            <div class="detail-item">
              <span class="detail-label">Target:</span>
              <span class="detail-value">&lt; 1%</span>
            </div>
          </div>
        {/if}
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Throughput</span>
          <span class="metric-trend">{getTrendIcon(performanceMetrics.throughput.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(performanceMetrics.throughput.value, 'throughput')}">
          {formatValue(performanceMetrics.throughput.value, performanceMetrics.throughput.unit)}
        </div>
        {#if showDetailedMetrics}
          <div class="metric-details">
            <div class="detail-item">
              <span class="detail-label">Peak:</span>
              <span class="detail-value">{formatValue(performanceMetrics.throughput.value * 1.5, performanceMetrics.throughput.unit)}</span>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
  
  <!-- Usage Metrics -->
  <div class="metrics-section">
    <div class="section-title">
      <span class="section-icon">📊</span>
      <h4>Usage</h4>
    </div>
    
    <div class="metrics-grid" class:compact={compactView}>
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Total Requests</span>
          <span class="metric-trend">{getTrendIcon(usageMetrics.totalRequests.trend)}</span>
        </div>
        <div class="metric-value">
          {formatValue(usageMetrics.totalRequests.value, usageMetrics.totalRequests.unit)}
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Active Users</span>
          <span class="metric-trend">{getTrendIcon(usageMetrics.activeUsers.trend)}</span>
        </div>
        <div class="metric-value">
          {formatValue(usageMetrics.activeUsers.value, usageMetrics.activeUsers.unit)}
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Requests/Min</span>
          <span class="metric-trend">{getTrendIcon(usageMetrics.requestsPerMinute.trend)}</span>
        </div>
        <div class="metric-value">
          {formatValue(usageMetrics.requestsPerMinute.value, usageMetrics.requestsPerMinute.unit)}
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Peak Usage</span>
          <span class="metric-trend">{getTrendIcon(usageMetrics.peakUsage.trend)}</span>
        </div>
        <div class="metric-value">
          {formatValue(usageMetrics.peakUsage.value, usageMetrics.peakUsage.unit)}
        </div>
      </div>
    </div>
  </div>
  
  <!-- Health Metrics -->
  <div class="metrics-section">
    <div class="section-title">
      <span class="section-icon">💓</span>
      <h4>Health</h4>
    </div>
    
    <div class="metrics-grid" class:compact={compactView}>
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">CPU Usage</span>
          <span class="metric-trend">{getTrendIcon(healthMetrics.cpuUsage.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(healthMetrics.cpuUsage.value, 'cpuUsage')}">
          {formatValue(healthMetrics.cpuUsage.value, healthMetrics.cpuUsage.unit)}
        </div>
        <div class="metric-bar">
          <div class="bar-fill" style="width: {healthMetrics.cpuUsage.value}%; background: {getMetricColor(healthMetrics.cpuUsage.value, 'cpuUsage')}"></div>
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Memory Usage</span>
          <span class="metric-trend">{getTrendIcon(healthMetrics.memoryUsage.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(healthMetrics.memoryUsage.value / 10, 'memoryUsage')}">
          {formatValue(healthMetrics.memoryUsage.value, healthMetrics.memoryUsage.unit)}
        </div>
        <div class="metric-bar">
          <div class="bar-fill" style="width: {Math.min(healthMetrics.memoryUsage.value / 10, 100)}%; background: {getMetricColor(healthMetrics.memoryUsage.value / 10, 'memoryUsage')}"></div>
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Network Latency</span>
          <span class="metric-trend">{getTrendIcon(healthMetrics.networkLatency.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(healthMetrics.networkLatency.value, 'networkLatency')}">
          {formatValue(healthMetrics.networkLatency.value, healthMetrics.networkLatency.unit)}
        </div>
      </div>
      
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">Health Score</span>
          <span class="metric-trend">{getTrendIcon(healthMetrics.healthScore.trend)}</span>
        </div>
        <div class="metric-value" style="color: {getMetricColor(healthMetrics.healthScore.value, 'healthScore')}">
          {formatValue(healthMetrics.healthScore.value, healthMetrics.healthScore.unit)}
        </div>
        <div class="metric-bar">
          <div class="bar-fill" style="width: {healthMetrics.healthScore.value}%; background: {getMetricColor(healthMetrics.healthScore.value, 'healthScore')}"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Historical Data -->
  {#if showHistoricalData}
    <div class="historical-section">
      <div class="historical-header">
        <h4>Historical Data</h4>
        <div class="historical-controls">
          <select bind:value={selectedTimeRange} class="time-range-select">
            {#each timeRanges as range}
              <option value={range.value}>{range.label}</option>
            {/each}
          </select>
          
          <select bind:value={selectedMetricCategory} class="category-select">
            {#each metricCategories as category}
              <option value={category.value}>{category.icon} {category.label}</option>
            {/each}
          </select>
        </div>
      </div>
      
      <div class="chart-container">
        {#if isLoading}
          <div class="chart-loading">Loading historical data...</div>
        {:else if historicalData.length > 0}
          <div class="simple-chart">
            <!-- Simple ASCII-style chart visualization -->
            <div class="chart-data">
              {#each historicalData.slice(-20) as point, i}
                <div class="chart-point" style="height: {point.value}%; background: #3b82f6;"></div>
              {/each}
            </div>
          </div>
        {:else}
          <div class="no-data">No historical data available</div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  .metrics-display {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .metrics-display.compact {
    padding: 12px;
  }
  
  .metrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #334155;
  }
  
  .header-title h3 {
    margin: 0 0 4px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
  }
  
  .last-updated {
    color: #64748b;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .loading-indicator {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
  }
  
  .action-btn {
    padding: 6px 12px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #e2e8f0;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }
  
  .action-btn:hover {
    background: #334155;
  }
  
  .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .metrics-section {
    margin-bottom: 24px;
  }
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .section-icon {
    font-size: 16px;
  }
  
  .section-title h4 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .metrics-grid.compact {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .metric-card {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 16px;
    transition: all 0.2s;
  }
  
  .metric-card:hover {
    border-color: #475569;
  }
  
  .metrics-grid.compact .metric-card {
    padding: 12px;
  }
  
  .metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .metric-name {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
  }
  
  .metric-trend {
    font-size: 12px;
  }
  
  .metric-value {
    color: #ffffff;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 8px;
  }
  
  .metrics-grid.compact .metric-value {
    font-size: 16px;
  }
  
  .metric-bar {
    height: 4px;
    background: #334155;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
  }
  
  .bar-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
  }
  
  .metric-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .detail-label {
    color: #64748b;
    font-size: 11px;
  }
  
  .detail-value {
    color: #e2e8f0;
    font-size: 11px;
    font-weight: 500;
  }
  
  .historical-section {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 16px;
  }
  
  .historical-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .historical-header h4 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .historical-controls {
    display: flex;
    gap: 8px;
  }
  
  .time-range-select,
  .category-select {
    padding: 4px 8px;
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    outline: none;
  }
  
  .time-range-select:focus,
  .category-select:focus {
    border-color: #3b82f6;
  }
  
  .chart-container {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .chart-loading,
  .no-data {
    color: #64748b;
    font-size: 14px;
    text-align: center;
  }
  
  .simple-chart {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: end;
    justify-content: center;
  }
  
  .chart-data {
    display: flex;
    align-items: end;
    gap: 2px;
    height: 100%;
    width: 100%;
  }
  
  .chart-point {
    flex: 1;
    min-height: 2px;
    border-radius: 1px;
    transition: all 0.2s;
  }
  
  .chart-point:hover {
    opacity: 0.8;
    transform: scaleY(1.1);
  }
  
  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: 1fr;
    }
    
    .metrics-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    
    .header-actions {
      justify-content: center;
    }
    
    .historical-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    
    .historical-controls {
      justify-content: center;
    }
  }
</style>