<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agent: any;
  export let maxLogEntries: number = 1000;
  export let autoRefresh: boolean = true;
  export let refreshInterval: number = 5000; // 5 seconds
  export let showFilters: boolean = true;
  export let compactView: boolean = false;
  export let realTimeMode: boolean = false;
  
  interface LogEntry {
    id: string;
    timestamp: string;
    level: 'info' | 'warning' | 'error' | 'debug' | 'success';
    category: 'request' | 'response' | 'system' | 'user' | 'performance' | 'security';
    message: string;
    details?: any;
    user?: string;
    requestId?: string;
    duration?: number;
    metadata?: Record<string, any>;
  }
  
  let logEntries: LogEntry[] = [];
  let filteredEntries: LogEntry[] = [];
  let isLoading = false;
  let isPaused = false;
  let isFollowingTail = true;
  let refreshTimer: NodeJS.Timeout | null = null;
  let logContainer: HTMLElement;
  
  // Filters
  let searchQuery = '';
  let selectedLevel: string = 'all';
  let selectedCategory: string = 'all';
  let dateRange = { start: '', end: '' };
  let showUserFilter = false;
  let selectedUser = '';
  let showAdvancedFilters = false;
  
  // Log levels configuration
  const logLevels = [
    { value: 'all', label: 'All Levels', color: '#94a3b8', icon: '📄' },
    { value: 'info', label: 'Info', color: '#3b82f6', icon: 'ℹ️' },
    { value: 'success', label: 'Success', color: '#10b981', icon: '✅' },
    { value: 'warning', label: 'Warning', color: '#f59e0b', icon: '⚠️' },
    { value: 'error', label: 'Error', color: '#ef4444', icon: '❌' },
    { value: 'debug', label: 'Debug', color: '#8b5cf6', icon: '🔍' }
  ];
  
  // Categories configuration
  const categories = [
    { value: 'all', label: 'All Categories', icon: '🔍' },
    { value: 'request', label: 'Requests', icon: '📨' },
    { value: 'response', label: 'Responses', icon: '📤' },
    { value: 'system', label: 'System', icon: '⚙️' },
    { value: 'user', label: 'User Actions', icon: '👤' },
    { value: 'performance', label: 'Performance', icon: '📊' },
    { value: 'security', label: 'Security', icon: '🔒' }
  ];
  
  // Statistics
  let logStats = {
    total: 0,
    info: 0,
    warning: 0,
    error: 0,
    debug: 0,
    success: 0,
    averageResponseTime: 0,
    lastError: null as LogEntry | null
  };
  
  onMount(() => {
    loadInitialLogs();
    if (autoRefresh) {
      startAutoRefresh();
    }
    if (realTimeMode) {
      connectToRealTimeStream();
    }
    
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  });
  
  onDestroy(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
  });
  
  async function loadInitialLogs() {
    isLoading = true;
    try {
      const logs = await fetchLogs(agent.id, maxLogEntries);
      logEntries = logs;
      updateStats();
      applyFilters();
    } catch (error) {
      console.error('Error loading logs:', error);
      dispatch('error', { error, message: 'Failed to load activity logs' });
    } finally {
      isLoading = false;
    }
  }\n  
  async function fetchLogs(agentId: string, limit: number): Promise<LogEntry[]> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const sampleLogs: LogEntry[] = [];
    const logTypes = ['info', 'warning', 'error', 'debug', 'success'];
    const categories = ['request', 'response', 'system', 'user', 'performance', 'security'];
    const users = ['user1', 'user2', 'user3', 'admin', 'system'];
    
    for (let i = 0; i < Math.min(limit, 50); i++) {
      const timestamp = new Date(Date.now() - i * 60000 * Math.random()).toISOString();
      const level = logTypes[Math.floor(Math.random() * logTypes.length)] as LogEntry['level'];
      const category = categories[Math.floor(Math.random() * categories.length)] as LogEntry['category'];
      const user = users[Math.floor(Math.random() * users.length)];
      
      sampleLogs.push({
        id: `log_${i}`,
        timestamp,
        level,
        category,
        message: generateLogMessage(level, category),
        user,
        requestId: `req_${Math.random().toString(36).substr(2, 9)}`,
        duration: Math.floor(Math.random() * 1000),
        metadata: {
          ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0...',
          sessionId: Math.random().toString(36).substr(2, 9)
        }
      });
    }
    
    return sampleLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }
  
  function generateLogMessage(level: string, category: string): string {
    const messages = {
      'info-request': 'User requested agent assistance',
      'info-response': 'Agent response generated successfully',
      'info-system': 'Agent status updated',
      'success-request': 'Request processed successfully',
      'success-response': 'Response delivered to user',
      'warning-performance': 'Response time exceeded 500ms',
      'warning-system': 'High memory usage detected',
      'error-request': 'Failed to process user request',
      'error-system': 'Agent connection timeout',
      'debug-performance': 'Performance metrics collected',
      'debug-security': 'Security check passed'
    };
    
    const key = `${level}-${category}`;
    return messages[key] || `${level} event in ${category}`;
  }
  
  function connectToRealTimeStream() {
    // Simulate real-time log streaming
    setInterval(() => {
      if (!isPaused && realTimeMode) {
        const newLog: LogEntry = {
          id: `log_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: (['info', 'warning', 'error', 'debug', 'success'][Math.floor(Math.random() * 5)]) as LogEntry['level'],
          category: (['request', 'response', 'system', 'user', 'performance', 'security'][Math.floor(Math.random() * 6)]) as LogEntry['category'],
          message: 'Real-time log entry',
          user: 'system',
          requestId: `req_${Math.random().toString(36).substr(2, 9)}`
        };
        
        addLogEntry(newLog);
      }
    }, 2000);
  }
  
  function addLogEntry(entry: LogEntry) {
    logEntries = [entry, ...logEntries.slice(0, maxLogEntries - 1)];
    updateStats();
    applyFilters();
    
    if (isFollowingTail) {
      scrollToBottom();
    }
    
    dispatch('newLogEntry', { entry, agent });
  }
  
  function startAutoRefresh() {
    refreshTimer = setInterval(async () => {
      if (!isPaused) {
        try {
          const newLogs = await fetchLogs(agent.id, 10);
          if (newLogs.length > 0) {
            logEntries = [...newLogs, ...logEntries].slice(0, maxLogEntries);
            updateStats();
            applyFilters();
          }
        } catch (error) {
          console.error('Error refreshing logs:', error);
        }
      }
    }, refreshInterval);
  }
  
  function updateStats() {
    logStats = {
      total: logEntries.length,
      info: logEntries.filter(e => e.level === 'info').length,
      warning: logEntries.filter(e => e.level === 'warning').length,
      error: logEntries.filter(e => e.level === 'error').length,
      debug: logEntries.filter(e => e.level === 'debug').length,
      success: logEntries.filter(e => e.level === 'success').length,
      averageResponseTime: logEntries.reduce((sum, e) => sum + (e.duration || 0), 0) / logEntries.length,
      lastError: logEntries.find(e => e.level === 'error') || null
    };
  }
  
  function applyFilters() {
    filteredEntries = logEntries.filter(entry => {
      // Search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        if (!entry.message.toLowerCase().includes(query) &&
            !entry.user?.toLowerCase().includes(query) &&
            !entry.requestId?.toLowerCase().includes(query)) {
          return false;
        }
      }
      
      // Level filter
      if (selectedLevel !== 'all' && entry.level !== selectedLevel) {
        return false;
      }
      
      // Category filter
      if (selectedCategory !== 'all' && entry.category !== selectedCategory) {
        return false;
      }
      
      // User filter
      if (selectedUser && entry.user !== selectedUser) {
        return false;
      }
      
      // Date range filter
      if (dateRange.start || dateRange.end) {
        const entryDate = new Date(entry.timestamp);
        if (dateRange.start && entryDate < new Date(dateRange.start)) return false;
        if (dateRange.end && entryDate > new Date(dateRange.end)) return false;
      }
      
      return true;
    });
  }
  
  function clearLogs() {
    logEntries = [];
    filteredEntries = [];
    updateStats();
    dispatch('logsCleared', { agent });
  }
  
  function exportLogs() {
    const exportData = {
      agent: {
        id: agent.id,
        name: agent.name,
        role: agent.role
      },
      logs: filteredEntries,
      stats: logStats,
      filters: {
        searchQuery,
        selectedLevel,
        selectedCategory,
        selectedUser,
        dateRange
      },
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${agent.name}-activity-log-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
  
  function scrollToBottom() {
    if (logContainer) {
      logContainer.scrollTop = logContainer.scrollHeight;
    }
  }
  
  function scrollToTop() {
    if (logContainer) {
      logContainer.scrollTop = 0;
    }
  }
  
  function togglePause() {
    isPaused = !isPaused;
  }
  
  function toggleFollowTail() {
    isFollowingTail = !isFollowingTail;
    if (isFollowingTail) {
      scrollToBottom();
    }
  }
  
  function clearFilters() {
    searchQuery = '';
    selectedLevel = 'all';
    selectedCategory = 'all';
    selectedUser = '';
    dateRange = { start: '', end: '' };
    applyFilters();
  }
  
  function getLevelConfig(level: string) {
    return logLevels.find(l => l.value === level) || logLevels[0];
  }
  
  function getCategoryConfig(category: string) {
    return categories.find(c => c.value === category) || categories[0];
  }
  
  function formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }
  
  function formatDuration(duration?: number): string {
    if (!duration) return '';
    return `${duration}ms`;
  }
  
  function getUniqueUsers(): string[] {
    return [...new Set(logEntries.map(e => e.user).filter(Boolean))];
  }
  
  // Reactive statements
  $: if (searchQuery || selectedLevel || selectedCategory || selectedUser || dateRange) {
    applyFilters();
  }
  
  $: uniqueUsers = getUniqueUsers();
</script>

<div class="activity-log-viewer" class:compact={compactView}>
  <!-- Header -->
  <div class="log-header">
    <div class="header-title">
      <h3>Activity Log</h3>
      <div class="log-info">
        {filteredEntries.length} of {logEntries.length} entries
        {#if isPaused}
          <span class="paused-indicator">⏸️ Paused</span>
        {/if}
      </div>
    </div>
    
    <div class="header-actions">
      <button class="action-btn" on:click={togglePause}>
        {isPaused ? '▶️' : '⏸️'} {isPaused ? 'Resume' : 'Pause'}
      </button>
      
      <button class="action-btn" on:click={toggleFollowTail} class:active={isFollowingTail}>
        📍 Follow Tail
      </button>
      
      <button class="action-btn" on:click={scrollToTop}>
        ⬆️ Top
      </button>
      
      <button class="action-btn" on:click={scrollToBottom}>
        ⬇️ Bottom
      </button>
      
      <button class="action-btn" on:click={exportLogs}>
        📤 Export
      </button>
      
      <button class="action-btn danger" on:click={clearLogs}>
        🗑️ Clear
      </button>
    </div>
  </div>
  
  <!-- Statistics -->
  <div class="log-stats">
    <div class="stat-item">
      <span class="stat-icon">📊</span>
      <span class="stat-label">Total:</span>
      <span class="stat-value">{logStats.total}</span>
    </div>
    
    <div class="stat-item">
      <span class="stat-icon">ℹ️</span>
      <span class="stat-label">Info:</span>
      <span class="stat-value">{logStats.info}</span>
    </div>
    
    <div class="stat-item">
      <span class="stat-icon">⚠️</span>
      <span class="stat-label">Warnings:</span>
      <span class="stat-value">{logStats.warning}</span>
    </div>
    
    <div class="stat-item">
      <span class="stat-icon">❌</span>
      <span class="stat-label">Errors:</span>
      <span class="stat-value">{logStats.error}</span>
    </div>
    
    <div class="stat-item">
      <span class="stat-icon">⚡</span>
      <span class="stat-label">Avg Response:</span>
      <span class="stat-value">{logStats.averageResponseTime.toFixed(0)}ms</span>
    </div>
  </div>
  
  <!-- Filters -->
  {#if showFilters}
    <div class="log-filters">
      <div class="filter-row">
        <div class="search-container">
          <input
            type="text"
            placeholder="Search logs..."
            bind:value={searchQuery}
            class="search-input"
          />
          <span class="search-icon">🔍</span>
        </div>
        
        <select bind:value={selectedLevel} class="filter-select">
          {#each logLevels as level}
            <option value={level.value}>{level.icon} {level.label}</option>
          {/each}
        </select>
        
        <select bind:value={selectedCategory} class="filter-select">
          {#each categories as category}
            <option value={category.value}>{category.icon} {category.label}</option>
          {/each}
        </select>
        
        <button class="filter-btn" on:click={() => showAdvancedFilters = !showAdvancedFilters}>
          🔧 Advanced
        </button>
        
        <button class="filter-btn" on:click={clearFilters}>
          🗑️ Clear
        </button>
      </div>
      
      {#if showAdvancedFilters}
        <div class="advanced-filters">
          <div class="filter-group">
            <label>Date Range:</label>
            <div class="date-range">
              <input type="datetime-local" bind:value={dateRange.start} />
              <span>to</span>
              <input type="datetime-local" bind:value={dateRange.end} />
            </div>
          </div>
          
          <div class="filter-group">
            <label>User:</label>
            <select bind:value={selectedUser} class="user-select">
              <option value="">All Users</option>
              {#each uniqueUsers as user}
                <option value={user}>{user}</option>
              {/each}
            </select>
          </div>
        </div>
      {/if}
    </div>
  {/if}
  
  <!-- Log Entries -->
  <div class="log-container" bind:this={logContainer}>
    {#if isLoading}
      <div class="loading-indicator">
        <span class="spinner">⏳</span>
        Loading activity logs...
      </div>
    {:else if filteredEntries.length === 0}
      <div class="empty-state">
        <span class="empty-icon">📄</span>
        <h4>No log entries found</h4>
        <p>Try adjusting your filters or check back later</p>
      </div>
    {:else}
      <div class="log-entries">
        {#each filteredEntries as entry (entry.id)}
          <div class="log-entry {entry.level}" class:compact={compactView}>
            <div class="entry-header">
              <div class="entry-level">
                <span class="level-icon" style="color: {getLevelConfig(entry.level).color}">
                  {getLevelConfig(entry.level).icon}
                </span>
                <span class="level-text">{entry.level.toUpperCase()}</span>
              </div>
              
              <div class="entry-meta">
                <span class="entry-category">
                  {getCategoryConfig(entry.category).icon}
                  {entry.category}
                </span>
                <span class="entry-timestamp">{formatTimestamp(entry.timestamp)}</span>
              </div>
            </div>
            
            <div class="entry-content">
              <div class="entry-message">{entry.message}</div>
              
              {#if !compactView}
                <div class="entry-details">
                  {#if entry.user}
                    <span class="detail-item">
                      <span class="detail-label">User:</span>
                      <span class="detail-value">{entry.user}</span>
                    </span>
                  {/if}
                  
                  {#if entry.requestId}
                    <span class="detail-item">
                      <span class="detail-label">Request ID:</span>
                      <span class="detail-value">{entry.requestId}</span>
                    </span>
                  {/if}
                  
                  {#if entry.duration}
                    <span class="detail-item">
                      <span class="detail-label">Duration:</span>
                      <span class="detail-value">{formatDuration(entry.duration)}</span>
                    </span>
                  {/if}
                </div>
              {/if}
              
              {#if entry.details && showAdvancedFilters}
                <div class="entry-raw-details">
                  <details>
                    <summary>Raw Details</summary>
                    <pre>{JSON.stringify(entry.details, null, 2)}</pre>
                  </details>
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
  
  <!-- Footer -->
  <div class="log-footer">
    <div class="footer-info">
      Showing {filteredEntries.length} entries
      {#if logStats.lastError}
        • Last error: {formatTimestamp(logStats.lastError.timestamp)}
      {/if}
    </div>
    
    <div class="footer-actions">
      <button class="footer-btn" on:click={scrollToTop}>↑ Top</button>
      <button class="footer-btn" on:click={scrollToBottom}>↓ Bottom</button>
    </div>
  </div>
</div>

<style>
  .activity-log-viewer {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .activity-log-viewer.compact {
    height: 400px;
  }
  
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #334155;
    background: #0f172a;
  }
  
  .header-title h3 {
    margin: 0 0 4px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
  }
  
  .log-info {
    color: #64748b;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .paused-indicator {
    background: #f59e0b;
    color: #1e293b;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
  }
  
  .header-actions {
    display: flex;
    gap: 6px;
  }
  
  .action-btn {
    padding: 6px 10px;
    background: #334155;
    border: 1px solid #475569;
    border-radius: 4px;
    color: #e2e8f0;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
  }
  
  .action-btn:hover {
    background: #475569;
  }
  
  .action-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
  }
  
  .action-btn.danger {
    background: #7f1d1d;
    border-color: #991b1b;
  }
  
  .action-btn.danger:hover {
    background: #991b1b;
  }
  
  .log-stats {
    display: flex;
    gap: 16px;
    padding: 12px 20px;
    background: #0f172a;
    border-bottom: 1px solid #334155;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
  }
  
  .stat-icon {
    font-size: 14px;
  }
  
  .stat-label {
    color: #94a3b8;
  }
  
  .stat-value {
    color: #ffffff;
    font-weight: 500;
  }
  
  .log-filters {
    padding: 12px 20px;
    border-bottom: 1px solid #334155;
    background: #1e293b;
  }
  
  .filter-row {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .search-container {
    position: relative;
    flex: 1;
  }
  
  .search-input {
    width: 100%;
    padding: 6px 32px 6px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
    outline: none;
  }
  
  .search-input:focus {
    border-color: #3b82f6;
  }
  
  .search-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 12px;
  }
  
  .filter-select {
    padding: 6px 8px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    outline: none;
  }
  
  .filter-select:focus {
    border-color: #3b82f6;
  }
  
  .filter-btn {
    padding: 6px 12px;
    background: #334155;
    border: 1px solid #475569;
    border-radius: 4px;
    color: #e2e8f0;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }
  
  .filter-btn:hover {
    background: #475569;
  }
  
  .advanced-filters {
    display: flex;
    gap: 16px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #334155;
  }
  
  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .filter-group label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
  }
  
  .date-range {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .date-range input {
    padding: 4px 6px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 11px;
    outline: none;
  }
  
  .date-range span {
    color: #64748b;
    font-size: 12px;
  }
  
  .user-select {
    padding: 4px 6px;
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    outline: none;
  }
  
  .log-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }
  
  .loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 40px;
    color: #64748b;
    font-size: 14px;
  }
  
  .spinner {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px;
    color: #64748b;
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-state h4 {
    margin: 0 0 8px 0;
    color: #94a3b8;
    font-size: 16px;
  }
  
  .empty-state p {
    margin: 0;
    font-size: 14px;
  }
  
  .log-entries {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .log-entry {
    background: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s;
  }
  
  .log-entry:hover {
    border-color: #475569;
  }
  
  .log-entry.compact {
    padding: 8px;
  }
  
  .log-entry.error {
    border-left: 4px solid #ef4444;
  }
  
  .log-entry.warning {
    border-left: 4px solid #f59e0b;
  }
  
  .log-entry.success {
    border-left: 4px solid #10b981;
  }
  
  .log-entry.info {
    border-left: 4px solid #3b82f6;
  }
  
  .log-entry.debug {
    border-left: 4px solid #8b5cf6;
  }
  
  .entry-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .entry-level {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .level-icon {
    font-size: 14px;
  }
  
  .level-text {
    font-size: 11px;
    font-weight: 600;
    color: #94a3b8;
  }
  
  .entry-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 11px;
    color: #64748b;
  }
  
  .entry-category {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .entry-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .entry-message {
    color: #e2e8f0;
    font-size: 13px;
    line-height: 1.4;
  }
  
  .entry-details {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .detail-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
  }
  
  .detail-label {
    color: #64748b;
  }
  
  .detail-value {
    color: #94a3b8;
    font-weight: 500;
  }
  
  .entry-raw-details {
    margin-top: 8px;
  }
  
  .entry-raw-details details {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 4px;
    padding: 8px;
  }
  
  .entry-raw-details summary {
    color: #94a3b8;
    font-size: 12px;
    cursor: pointer;
  }
  
  .entry-raw-details pre {
    margin: 8px 0 0 0;
    color: #e2e8f0;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
  
  .log-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    border-top: 1px solid #334155;
    background: #0f172a;
  }
  
  .footer-info {
    color: #64748b;
    font-size: 12px;
  }
  
  .footer-actions {
    display: flex;
    gap: 6px;
  }
  
  .footer-btn {
    padding: 4px 8px;
    background: #334155;
    border: 1px solid #475569;
    border-radius: 3px;
    color: #94a3b8;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.2s;
  }
  
  .footer-btn:hover {
    background: #475569;
    color: #e2e8f0;
  }
  
  @media (max-width: 768px) {
    .log-header {
      flex-direction: column;
      gap: 8px;
    }
    
    .header-actions {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .log-stats {
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .filter-row {
      flex-direction: column;
      align-items: stretch;
    }
    
    .advanced-filters {
      flex-direction: column;
    }
    
    .entry-details {
      flex-direction: column;
      gap: 4px;
    }
  }
</style>