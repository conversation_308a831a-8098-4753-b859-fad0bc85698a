<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  export let isOpen = false;
  export let maxAgents = 2;
  export let apiKeys = {
    gemini: [],
    claude: [],
    openai: []
  };
  
  const dispatch = createEventDispatcher<{
    updateConfig: {
      maxAgents: number;
      apiKeys: any;
    };
    close: void;
  }>();

  let localMaxAgents = maxAgents;
  let localApiKeys = { ...apiKeys };

  function addApiKey(provider: string) {
    if (!localApiKeys[provider]) {
      localApiKeys[provider] = [];
    }
    localApiKeys[provider] = [...localApiKeys[provider], ''];
  }

  function removeApiKey(provider: string, index: number) {
    localApiKeys[provider] = localApiKeys[provider].filter((_, i) => i !== index);
  }

  function updateApiKey(provider: string, index: number, value: string) {
    localApiKeys[provider][index] = value;
  }

  function saveConfig() {
    dispatch('updateConfig', {
      maxAgents: localMaxAgents,
      apiKeys: localApiKeys
    });
  }

  function closePanel() {
    dispatch('close');
  }

  $: tierName = {
    2: 'Free',
    3: 'Pro ($14/mo)',
    5: 'Dev+ ($20/mo)'
  }[localMaxAgents] || 'Enterprise';
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Configuration</h2>
        <button
          on:click={closePanel}
          class="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      <!-- Agent Configuration -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3">Agent Settings</h3>
        
        <div class="mb-4">
          <label for="maxAgents" class="block text-sm font-medium mb-2">
            Maximum Agents: {localMaxAgents} ({tierName})
          </label>
          <input
            id="maxAgents"
            type="range"
            min="2"
            max="5"
            bind:value={localMaxAgents}
            class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
          />
          <div class="flex justify-between text-xs text-gray-400 mt-1">
            <span>2 (Free)</span>
            <span>3 (Pro)</span>
            <span>5 (Dev+)</span>
          </div>
        </div>
      </div>

      <!-- API Keys Configuration -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3">API Keys</h3>
        
        {#each Object.entries(localApiKeys) as [provider, keys]}
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <label class="text-sm font-medium capitalize">
                {provider} Keys ({keys.length})
              </label>
              <button
                on:click={() => addApiKey(provider)}
                class="text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded"
              >
                Add Key
              </button>
            </div>
            
            {#each keys as key, index}
              <div class="flex gap-2 mb-2">
                <input
                  type="password"
                  placeholder="Enter API key..."
                  value={key}
                  on:input={(e) => updateApiKey(provider, index, e.target.value)}
                  class="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  on:click={() => removeApiKey(provider, index)}
                  class="bg-red-600 hover:bg-red-700 px-3 py-2 rounded text-white"
                >
                  ✕
                </button>
              </div>
            {/each}
            
            {#if keys.length === 0}
              <p class="text-gray-400 text-sm">No keys configured</p>
            {/if}
          </div>
        {/each}
      </div>

      <!-- Safety Settings -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3">Safety Settings</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-400">Max Loop Time:</span>
            <span class="text-white ml-2">15 minutes</span>
          </div>
          <div>
            <span class="text-gray-400">Max Rounds:</span>
            <span class="text-white ml-2">10 rounds</span>
          </div>
          <div>
            <span class="text-gray-400">Consensus Required:</span>
            <span class="text-green-400 ml-2">100%</span>
          </div>
          <div>
            <span class="text-gray-400">Secret Scrubbing:</span>
            <span class="text-green-400 ml-2">Enabled</span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-3">
        <button
          on:click={closePanel}
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-medium"
        >
          Cancel
        </button>
        <button
          on:click={saveConfig}
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium"
        >
          Save Changes
        </button>
      </div>
    </div>
  </div>
{/if}