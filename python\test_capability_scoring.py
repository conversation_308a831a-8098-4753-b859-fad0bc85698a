#!/usr/bin/env python3
"""
Test script to demonstrate the agent capability scoring system
"""

import sys
sys.path.insert(0, '/home/<USER>/Metamorphic-reactor-vs/python')

from agents.agent_factory import AgentFactory, CapabilityCategory, CapabilityLevel, ScoringMetric
from agents.agent_types import Agent<PERSON><PERSON>, AgentCreationRequest
from datetime import datetime

def test_capability_scoring_system():
    """Test the capability scoring system"""
    
    print("=== Testing Agent Capability Scoring System ===\n")
    
    # Create factory instance
    factory = AgentFactory()
    
    # Create some test agents
    print("1. Creating test agents...")
    
    # Create a coder agent
    coder_request = AgentCreationRequest(
        name="Python Coder",
        role=AgentRole.CODER,
        model="gpt-4",
        prompt="You are an expert Python developer.",
        capabilities=["code_generation", "debugging", "testing"]
    )
    coder_profile = factory.create_agent_profile(coder_request)
    
    # Create a planner agent
    planner_request = AgentCreationRequest(
        name="Task Planner",
        role=AgentRole.PLANNER,
        model="gpt-4",
        prompt="You are a strategic task planner.",
        capabilities=["task_decomposition", "problem_solving"]
    )
    planner_profile = factory.create_agent_profile(planner_request)
    
    print(f"Created agents: {coder_profile.name}, {planner_profile.name}")
    
    # Test capability evaluation
    print("\n2. Testing capability evaluation...")
    
    # Evaluate coder's code generation capability
    code_gen_context = {
        "task_id": "task_001",
        "success": True,
        "completion_time": 45,  # seconds
        "quality_score": 0.85,
        "accuracy": 0.9,
        "speed": 0.8,
        "consistency": 0.85
    }
    
    evaluation1 = factory.capability_scorer.evaluate_agent_capability(
        coder_profile.id, 
        "code_generation", 
        code_gen_context
    )
    factory.capability_scorer.update_capability_profile(coder_profile.id, evaluation1)
    
    print(f"Evaluated {coder_profile.name} for code_generation: {evaluation1.score:.2f}")
    
    # Evaluate planner's task decomposition capability
    task_decomp_context = {
        "task_id": "task_002",
        "success": True,
        "completion_time": 30,
        "quality_score": 0.9,
        "accuracy": 0.95,
        "complexity_handling": 0.8,
        "consistency": 0.9
    }
    
    evaluation2 = factory.capability_scorer.evaluate_agent_capability(
        planner_profile.id,
        "task_decomposition",
        task_decomp_context
    )
    factory.capability_scorer.update_capability_profile(planner_profile.id, evaluation2)
    
    print(f"Evaluated {planner_profile.name} for task_decomposition: {evaluation2.score:.2f}")
    
    # Test multiple evaluations to see score evolution
    print("\n3. Testing score evolution with multiple evaluations...")
    
    # Add more evaluations for the coder
    for i in range(3):
        context = {
            "task_id": f"task_00{i+3}",
            "success": True,
            "completion_time": 40 + i*5,
            "quality_score": 0.8 + i*0.05,
            "accuracy": 0.85 + i*0.03,
            "speed": 0.9 - i*0.02,
            "consistency": 0.8 + i*0.04
        }
        
        eval = factory.capability_scorer.evaluate_agent_capability(
            coder_profile.id,
            "code_generation",
            context
        )
        factory.capability_scorer.update_capability_profile(coder_profile.id, eval)
    
    # Check updated capability profile
    coder_cap_profile = factory.capability_scorer.get_capability_profile(coder_profile.id)
    if coder_cap_profile:
        code_gen_score = coder_cap_profile.capability_scores.get("code_generation")
        if code_gen_score:
            print(f"Coder's code_generation evolved to: {code_gen_score.score:.2f} ({code_gen_score.level.value})")
            print(f"Evidence count: {code_gen_score.evidence_count}")
    
    # Test agent fitness scoring
    print("\n4. Testing agent fitness scoring...")
    
    required_caps = ["code_generation", "debugging"]
    coder_fitness = factory.capability_scorer.get_agent_fitness_score(coder_profile.id, required_caps)
    planner_fitness = factory.capability_scorer.get_agent_fitness_score(planner_profile.id, required_caps)
    
    print(f"Coder fitness for {required_caps}: {coder_fitness:.2f}")
    print(f"Planner fitness for {required_caps}: {planner_fitness:.2f}")
    
    # Test finding best agent for task
    print("\n5. Testing best agent selection...")
    
    best_agent = factory.capability_scorer.find_best_agent_for_task(
        required_capabilities=["code_generation", "debugging"],
        min_score=0.5
    )
    
    if best_agent:
        best_profile = factory.get_agent_profile(best_agent)
        print(f"Best agent for coding task: {best_profile.name if best_profile else 'Unknown'}")
    else:
        print("No suitable agent found for coding task")
    
    # Test capability leaderboard
    print("\n6. Testing capability leaderboard...")
    
    leaderboard = factory.capability_scorer.get_capability_leaderboard("code_generation", limit=5)
    print("Code generation leaderboard:")
    for i, entry in enumerate(leaderboard, 1):
        agent_profile = factory.get_agent_profile(entry["agent_id"])
        agent_name = agent_profile.name if agent_profile else "Unknown"
        print(f"  {i}. {agent_name}: {entry['score']:.2f} ({entry['level']})")
    
    # Test capability statistics
    print("\n7. Testing capability statistics...")
    
    stats = factory.capability_scorer.get_capability_statistics()
    print(f"Total capability profiles: {stats['total_profiles']}")
    print(f"Total evaluations: {stats['total_evaluations']}")
    print(f"Available capabilities: {list(stats['capability_averages'].keys())}")
    print(f"Available benchmarks: {stats['available_benchmarks']}")
    
    # Test individual capability profile
    print("\n8. Testing individual capability profile...")
    
    if coder_cap_profile:
        print(f"Agent: {coder_profile.name}")
        print(f"Overall score: {coder_cap_profile.overall_score:.2f}")
        print(f"Strengths: {coder_cap_profile.strengths}")
        print(f"Weaknesses: {coder_cap_profile.weaknesses}")
        print(f"Recommendations: {coder_cap_profile.recommendations}")
        
        # Show detailed capability scores
        print("\nDetailed capability scores:")
        for cap, score in coder_cap_profile.capability_scores.items():
            print(f"  {cap}: {score.score:.2f} ({score.level.value}) - {score.evidence_count} evaluations")
    
    print("\n=== All capability scoring tests completed successfully! ===")

if __name__ == "__main__":
    test_capability_scoring_system()