{"extends": "@tsconfig/svelte/tsconfig.json", "compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["vite/client"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.js", "src/**/*.svelte"], "references": [{"path": "./tsconfig.node.json"}]}