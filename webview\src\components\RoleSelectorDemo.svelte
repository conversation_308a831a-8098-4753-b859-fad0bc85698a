<script lang="ts">
  import RoleSelector from './RoleSelector.svelte';
  
  let selectedRole1 = 'coder';
  let selectedRole2 = 'debugger';
  let selectedRole3 = 'planner';
  let selectedRole4 = 'critic';
  let selectedRoles = ['coder', 'debugger'];
  
  function handleRoleSelected(event) {
    console.log('Role selected:', event.detail);
  }
  
  function handleRolesChanged(event) {
    console.log('Roles changed:', event.detail);
  }
</script>

<div class="max-w-7xl mx-auto p-6 space-y-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-white mb-2">Role Selector Component</h1>
    <p class="text-gray-400">Flexible role selection interface for different agent types</p>
  </div>
  
  <!-- Grid Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Grid Layout</h2>
    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Default Grid</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="grid"
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Grid with Capabilities</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="grid"
          showCapabilities={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Grid with Metrics</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="grid"
          showMetrics={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- List Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">List Layout</h2>
    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Full List</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole2}
          layout="list"
          showCapabilities={true}
          showMetrics={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Development Roles Only</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole2}
          layout="list"
          filterByCategory="development"
          showCapabilities={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Dropdown Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Dropdown Layout</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Default Dropdown</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole3}
          layout="dropdown"
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Compact Dropdown</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole3}
          layout="dropdown"
          size="small"
          showDescriptions={false}
          on:roleSelected={handleRoleSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Tabs Layout -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Tabs Layout</h2>
    <RoleSelector 
      bind:selectedRole={selectedRole4}
      layout="tabs"
      showCapabilities={true}
      on:roleSelected={handleRoleSelected}
    />
  </section>
  
  <!-- Multiple Selection -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Multiple Selection</h2>
    <div class="space-y-4">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Select Multiple Roles</h3>
        <RoleSelector 
          bind:selectedRoles={selectedRoles}
          layout="grid"
          allowMultiple={true}
          showCapabilities={true}
          on:rolesChanged={handleRolesChanged}
        />
      </div>
      
      <div class="mt-4 p-4 bg-gray-800 rounded-lg">
        <h4 class="font-medium text-white mb-2">Selected Roles:</h4>
        <div class="flex flex-wrap gap-2">
          {#each selectedRoles as role}
            <span class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm">{role}</span>
          {/each}
        </div>
      </div>
    </div>
  </section>
  
  <!-- Size Variations -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Size Variations</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Small</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="dropdown"
          size="small"
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Medium (Default)</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="dropdown"
          size="medium"
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Large</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="dropdown"
          size="large"
          on:roleSelected={handleRoleSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Category Filtering -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Category Filtering</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Development Roles</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="grid"
          filterByCategory="development"
          showCapabilities={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-white mb-3">Management Roles</h3>
        <RoleSelector 
          bind:selectedRole={selectedRole1}
          layout="grid"
          filterByCategory="management"
          showCapabilities={true}
          on:roleSelected={handleRoleSelected}
        />
      </div>
    </div>
  </section>
  
  <!-- Current Selections -->
  <section class="bg-gray-900 p-6 rounded-lg border border-gray-700">
    <h2 class="text-xl font-semibold text-white mb-4">Current Selections</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-400">Grid:</span>
          <span class="text-white font-medium">{selectedRole1}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">List:</span>
          <span class="text-white font-medium">{selectedRole2}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">Dropdown:</span>
          <span class="text-white font-medium">{selectedRole3}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">Tabs:</span>
          <span class="text-white font-medium">{selectedRole4}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">Multiple:</span>
          <span class="text-white font-medium">{selectedRoles.join(', ')}</span>
        </div>
      </div>
      
      <div class="text-sm text-gray-400">
        <p>The RoleSelector component supports:</p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>Multiple layout options (grid, list, dropdown, tabs)</li>
          <li>Category filtering (development, management, design, quality, coordination)</li>
          <li>Size variations (small, medium, large)</li>
          <li>Single and multiple selection modes</li>
          <li>Capability and metrics display</li>
          <li>Recommended role indicators</li>
          <li>Role-specific colors and icons</li>
          <li>Responsive design</li>
          <li>Disabled state support</li>
        </ul>
      </div>
    </div>
  </section>
</div>