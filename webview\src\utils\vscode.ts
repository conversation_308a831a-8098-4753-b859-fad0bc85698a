// VS Code API interface for webview communication
interface VsCodeApi {
  postMessage(message: any): void;
  setState(state: any): void;
  getState(): any;
}

// Get VS Code API (available in webview context)
declare function acquireVsCodeApi(): VsCodeApi;

// Create a mock API for development/testing
const createMockVsCodeApi = (): VsCodeApi => ({
  postMessage: (message: any) => {
    console.log('Mock VS Code API - postMessage:', message);
    // In development, you could emit custom events or handle messages differently
    window.dispatchEvent(new CustomEvent('vscode-message', { detail: message }));
  },
  setState: (state: any) => {
    console.log('Mock VS Code API - setState:', state);
    localStorage.setItem('vscode-webview-state', JSON.stringify(state));
  },
  getState: () => {
    console.log('Mock VS Code API - getState');
    const stored = localStorage.getItem('vscode-webview-state');
    return stored ? JSON.parse(stored) : null;
  }
});

// Safely acquire VS Code API with fallback
function getVs<PERSON>ode<PERSON><PERSON>(): VsCodeApi {
  try {
    // Check if we're in a VS Code webview context
    if (typeof acquireVsCodeApi !== 'undefined') {
      return acquireVsCodeApi();
    }
  } catch (error) {
    console.warn('VS Code API not available, using mock implementation:', error);
  }

  // Fallback to mock implementation for development
  return createMockVsCodeApi();
}

export const vscodeApi = getVsCodeApi();