<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let selectedTeam: string = '';
  export let teams: Team[] = [];
  export let placeholder: string = 'Select team...';
  export let allowCreateNew: boolean = true;
  export let disabled: boolean = false;
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let variant: 'dropdown' | 'grid' | 'list' = 'dropdown';
  export let showTeamInfo: boolean = true;
  
  interface Team {
    id: string;
    name: string;
    description?: string;
    memberCount?: number;
    maxMembers?: number;
    status: 'active' | 'inactive' | 'full';
    color?: string;
    tags?: string[];
    created?: string;
    lead?: string;
  }
  
  // Default teams if none provided
  const defaultTeams: Team[] = [
    {
      id: 'dev-team-alpha',
      name: 'Development Team Alpha',
      description: 'Frontend and UI/UX specialists',
      memberCount: 4,
      maxMembers: 6,
      status: 'active',
      color: '#3b82f6',
      tags: ['frontend', 'react', 'design'],
      created: '2024-01-15',
      lead: '<PERSON>'
    },
    {
      id: 'dev-team-beta',
      name: 'Development Team Beta',
      description: 'Backend and infrastructure experts',
      memberCount: 5,
      maxMembers: 6,
      status: 'active',
      color: '#10b981',
      tags: ['backend', 'devops', 'databases'],
      created: '2024-01-20',
      lead: 'Alex Rodriguez'
    },
    {
      id: 'qa-team',
      name: 'Quality Assurance Team',
      description: 'Testing and quality specialists',
      memberCount: 3,
      maxMembers: 5,
      status: 'active',
      color: '#f59e0b',
      tags: ['testing', 'automation', 'quality'],
      created: '2024-02-01',
      lead: 'Emily Johnson'
    },
    {
      id: 'data-team',
      name: 'Data Analytics Team',
      description: 'Data science and analytics experts',
      memberCount: 2,
      maxMembers: 4,
      status: 'active',
      color: '#8b5cf6',
      tags: ['data', 'analytics', 'ml'],
      created: '2024-02-10',
      lead: 'Dr. Michael Zhang'
    },
    {
      id: 'security-team',
      name: 'Security Team',
      description: 'Cybersecurity and compliance specialists',
      memberCount: 2,
      maxMembers: 3,
      status: 'active',
      color: '#ef4444',
      tags: ['security', 'compliance', 'audit'],
      created: '2024-01-25',
      lead: 'Lisa Park'
    },
    {
      id: 'archived-team',
      name: 'Project Phoenix (Archived)',
      description: 'Completed legacy project team',
      memberCount: 0,
      maxMembers: 8,
      status: 'inactive',
      color: '#6b7280',
      tags: ['archived', 'legacy'],
      created: '2023-06-15',
      lead: 'Former Team'
    }
  ];
  
  $: availableTeams = teams.length > 0 ? teams : defaultTeams;
  $: activeTeams = availableTeams.filter(team => team.status === 'active');
  $: selectedTeamData = availableTeams.find(team => team.id === selectedTeam);
  
  let showDropdown = false;
  let searchQuery = '';
  let newTeamName = '';
  let showCreateForm = false;
  
  // Size classes
  $: sizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  }[size];
  
  $: buttonSizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-5 py-4 text-lg'
  }[size];
  
  // Filter teams based on search
  $: filteredTeams = availableTeams.filter(team => 
    team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    team.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    team.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );
  
  function selectTeam(teamId: string) {
    selectedTeam = teamId;
    showDropdown = false;
    searchQuery = '';
    dispatch('teamSelected', { 
      teamId, 
      team: availableTeams.find(t => t.id === teamId) 
    });
  }
  
  function toggleDropdown() {
    if (!disabled) {
      showDropdown = !showDropdown;
    }
  }
  
  function handleCreateNew() {
    showCreateForm = true;
    showDropdown = false;
  }
  
  function createNewTeam() {
    if (!newTeamName.trim()) return;
    
    const newTeam: Team = {
      id: `custom-${Date.now()}`,
      name: newTeamName.trim(),
      description: 'Custom team',
      memberCount: 0,
      maxMembers: 6,
      status: 'active',
      color: '#6366f1',
      tags: ['custom'],
      created: new Date().toISOString().split('T')[0],
      lead: 'You'
    };
    
    dispatch('teamCreated', { team: newTeam });
    selectedTeam = newTeam.id;
    
    newTeamName = '';
    showCreateForm = false;
    
    dispatch('teamSelected', { 
      teamId: newTeam.id, 
      team: newTeam 
    });
  }
  
  function cancelCreate() {
    newTeamName = '';
    showCreateForm = false;
  }
  
  function getTeamStatusColor(status: Team['status']): string {
    switch (status) {
      case 'active': return '#10b981';
      case 'full': return '#f59e0b';
      case 'inactive': return '#6b7280';
      default: return '#6b7280';
    }
  }
  
  function getTeamStatusText(team: Team): string {
    if (team.status === 'inactive') return 'Inactive';
    if (team.memberCount && team.maxMembers && team.memberCount >= team.maxMembers) return 'Full';
    return 'Active';
  }
  
  function formatMemberCount(team: Team): string {
    if (!team.memberCount && !team.maxMembers) return '';
    return `${team.memberCount || 0}/${team.maxMembers || '∞'} members`;
  }
</script>

<div class="team-selector {variant} {sizeClasses}" class:disabled>
  {#if variant === 'dropdown'}
    <!-- Dropdown Layout -->
    <div class="dropdown-container">
      <button
        type="button"
        class="team-dropdown-trigger {buttonSizeClasses}"
        class:active={showDropdown}
        on:click={toggleDropdown}
        {disabled}
      >
        <div class="selected-team-display">
          {#if selectedTeamData}
            <div class="team-indicator" style="background-color: {selectedTeamData.color}"></div>
            <div class="team-info">
              <span class="team-name">{selectedTeamData.name}</span>
              {#if showTeamInfo && selectedTeamData.memberCount !== undefined}
                <span class="team-meta">{formatMemberCount(selectedTeamData)}</span>
              {/if}
            </div>
          {:else}
            <span class="placeholder">{placeholder}</span>
          {/if}
        </div>
        <svg class="dropdown-arrow" class:rotated={showDropdown} viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
        </svg>
      </button>
      
      {#if showDropdown}
        <div class="dropdown-menu">
          <!-- Search -->
          <div class="dropdown-search">
            <input
              type="text"
              placeholder="Search teams..."
              bind:value={searchQuery}
              class="search-input"
            />
          </div>
          
          <!-- Team List -->
          <div class="team-list">
            {#each filteredTeams as team (team.id)}
              <button
                type="button"
                class="team-option"
                class:selected={selectedTeam === team.id}
                class:inactive={team.status === 'inactive'}
                on:click={() => selectTeam(team.id)}
              >
                <div class="team-indicator" style="background-color: {team.color}"></div>
                <div class="team-details">
                  <div class="team-header">
                    <span class="team-name">{team.name}</span>
                    <span 
                      class="team-status"
                      style="color: {getTeamStatusColor(team.status)}"
                    >
                      {getTeamStatusText(team)}
                    </span>
                  </div>
                  {#if team.description}
                    <div class="team-description">{team.description}</div>
                  {/if}
                  <div class="team-meta">
                    {#if team.memberCount !== undefined}
                      <span>{formatMemberCount(team)}</span>
                    {/if}
                    {#if team.lead}
                      <span>Led by {team.lead}</span>
                    {/if}
                  </div>
                  {#if team.tags && team.tags.length > 0}
                    <div class="team-tags">
                      {#each team.tags.slice(0, 3) as tag}
                        <span class="team-tag">{tag}</span>
                      {/each}
                    </div>
                  {/if}
                </div>
              </button>
            {/each}
            
            {#if filteredTeams.length === 0}
              <div class="no-teams">No teams found</div>
            {/if}
          </div>
          
          <!-- Create New Team -->
          {#if allowCreateNew}
            <div class="dropdown-footer">
              <button
                type="button"
                class="create-team-btn"
                on:click={handleCreateNew}
              >
                + Create New Team
              </button>
            </div>
          {/if}
        </div>
      {/if}
    </div>
    
  {:else if variant === 'grid'}
    <!-- Grid Layout -->
    <div class="grid-container">
      <div class="team-grid">
        {#each activeTeams as team (team.id)}
          <button
            type="button"
            class="team-card"
            class:selected={selectedTeam === team.id}
            on:click={() => selectTeam(team.id)}
            {disabled}
          >
            <div class="card-header">
              <div class="team-indicator" style="background-color: {team.color}"></div>
              <span class="team-status" style="color: {getTeamStatusColor(team.status)}">
                {getTeamStatusText(team)}
              </span>
            </div>
            <h3 class="team-name">{team.name}</h3>
            {#if team.description}
              <p class="team-description">{team.description}</p>
            {/if}
            <div class="card-footer">
              {#if team.memberCount !== undefined}
                <span class="member-count">{formatMemberCount(team)}</span>
              {/if}
              {#if team.lead}
                <span class="team-lead">Led by {team.lead}</span>
              {/if}
            </div>
            {#if team.tags && team.tags.length > 0}
              <div class="team-tags">
                {#each team.tags.slice(0, 3) as tag}
                  <span class="team-tag">{tag}</span>
                {/each}
              </div>
            {/if}
          </button>
        {/each}
        
        {#if allowCreateNew}
          <button
            type="button"
            class="team-card create-card"
            on:click={handleCreateNew}
            {disabled}
          >
            <div class="create-content">
              <div class="create-icon">+</div>
              <span>Create New Team</span>
            </div>
          </button>
        {/if}
      </div>
    </div>
    
  {:else if variant === 'list'}
    <!-- List Layout -->
    <div class="list-container">
      <div class="team-list-header">
        <h3>Available Teams</h3>
        <input
          type="text"
          placeholder="Search teams..."
          bind:value={searchQuery}
          class="search-input"
        />
      </div>
      
      <div class="team-list vertical">
        {#each filteredTeams as team (team.id)}
          <label class="team-list-item">
            <input
              type="radio"
              name="team-selection"
              value={team.id}
              bind:group={selectedTeam}
              on:change={() => selectTeam(team.id)}
              {disabled}
            />
            <div class="list-item-content">
              <div class="team-indicator" style="background-color: {team.color}"></div>
              <div class="team-details">
                <div class="team-header">
                  <span class="team-name">{team.name}</span>
                  <span 
                    class="team-status"
                    style="color: {getTeamStatusColor(team.status)}"
                  >
                    {getTeamStatusText(team)}
                  </span>
                </div>
                {#if team.description}
                  <div class="team-description">{team.description}</div>
                {/if}
                <div class="team-meta">
                  {#if team.memberCount !== undefined}
                    <span>{formatMemberCount(team)}</span>
                  {/if}
                  {#if team.lead}
                    <span>Led by {team.lead}</span>
                  {/if}
                </div>
              </div>
            </div>
          </label>
        {/each}
      </div>
      
      {#if allowCreateNew}
        <button
          type="button"
          class="create-team-btn list"
          on:click={handleCreateNew}
          {disabled}
        >
          + Create New Team
        </button>
      {/if}
    </div>
  {/if}
</div>

<!-- Create New Team Modal -->
{#if showCreateForm}
  <div class="modal-overlay" on:click={cancelCreate}>
    <div class="modal-content" on:click|stopPropagation>
      <h3>Create New Team</h3>
      <div class="form-group">
        <label for="team-name">Team Name</label>
        <input
          id="team-name"
          type="text"
          bind:value={newTeamName}
          placeholder="Enter team name..."
          class="form-input"
          on:keydown={(e) => e.key === 'Enter' && createNewTeam()}
        />
      </div>
      <div class="modal-actions">
        <button type="button" class="btn-secondary" on:click={cancelCreate}>
          Cancel
        </button>
        <button 
          type="button" 
          class="btn-primary" 
          on:click={createNewTeam}
          disabled={!newTeamName.trim()}
        >
          Create Team
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .team-selector {
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .team-selector.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  /* Dropdown Layout */
  .dropdown-container {
    position: relative;
  }
  
  .team-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .team-dropdown-trigger:hover {
    border-color: #4b5563;
  }
  
  .team-dropdown-trigger.active {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .selected-team-display {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }
  
  .team-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .team-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
  
  .team-name {
    font-weight: 500;
    color: #ffffff;
  }
  
  .team-meta {
    font-size: 12px;
    color: #9ca3af;
  }
  
  .placeholder {
    color: #9ca3af;
  }
  
  .dropdown-arrow {
    width: 20px;
    height: 20px;
    transition: transform 0.2s;
    flex-shrink: 0;
  }
  
  .dropdown-arrow.rotated {
    transform: rotate(180deg);
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    margin-top: 4px;
    max-height: 400px;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  .dropdown-search {
    padding: 12px;
    border-bottom: 1px solid #374151;
  }
  
  .search-input {
    width: 100%;
    padding: 8px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
  }
  
  .search-input:focus {
    border-color: #3b82f6;
  }
  
  .search-input::placeholder {
    color: #9ca3af;
  }
  
  .team-list {
    max-height: 280px;
    overflow-y: auto;
  }
  
  .team-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    padding: 12px;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #374151;
  }
  
  .team-option:last-child {
    border-bottom: none;
  }
  
  .team-option:hover {
    background: #374151;
  }
  
  .team-option.selected {
    background: #1e40af;
  }
  
  .team-option.inactive {
    opacity: 0.6;
  }
  
  .team-details {
    flex: 1;
  }
  
  .team-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .team-description {
    font-size: 13px;
    color: #d1d5db;
    margin-bottom: 6px;
  }
  
  .team-status {
    font-size: 12px;
    font-weight: 500;
  }
  
  .team-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 6px;
  }
  
  .team-tag {
    padding: 2px 6px;
    background: #374151;
    color: #d1d5db;
    border-radius: 3px;
    font-size: 11px;
  }
  
  .dropdown-footer {
    padding: 12px;
    border-top: 1px solid #374151;
  }
  
  .create-team-btn {
    width: 100%;
    padding: 8px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }
  
  .create-team-btn:hover {
    background: #4b5563;
  }
  
  .create-team-btn.list {
    margin-top: 12px;
  }
  
  .no-teams {
    padding: 24px;
    text-align: center;
    color: #9ca3af;
    font-style: italic;
  }
  
  /* Grid Layout */
  .grid-container {
    width: 100%;
  }
  
  .team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .team-card {
    padding: 16px;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
  }
  
  .team-card:hover {
    border-color: #4b5563;
    transform: translateY(-1px);
  }
  
  .team-card.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }
  
  .team-card.create-card {
    border-style: dashed;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
  }
  
  .create-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #9ca3af;
  }
  
  .create-icon {
    font-size: 24px;
    font-weight: bold;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .card-footer {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #9ca3af;
  }
  
  /* List Layout */
  .list-container {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .team-list-header {
    padding: 16px;
    border-bottom: 1px solid #374151;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }
  
  .team-list-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 16px;
  }
  
  .team-list-header .search-input {
    max-width: 200px;
  }
  
  .team-list.vertical {
    max-height: none;
  }
  
  .team-list-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #374151;
    transition: background-color 0.2s;
  }
  
  .team-list-item:last-child {
    border-bottom: none;
  }
  
  .team-list-item:hover {
    background: #374151;
  }
  
  .team-list-item input[type="radio"] {
    margin-top: 2px;
  }
  
  .list-item-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    flex: 1;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }
  
  .modal-content {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 24px;
    min-width: 400px;
    max-width: 90vw;
  }
  
  .modal-content h3 {
    margin: 0 0 16px 0;
    color: #ffffff;
  }
  
  .form-group {
    margin-bottom: 16px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 6px;
    color: #d1d5db;
    font-weight: 500;
  }
  
  .form-input {
    width: 100%;
    padding: 10px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #ffffff;
    font-size: 14px;
    outline: none;
  }
  
  .form-input:focus {
    border-color: #3b82f6;
  }
  
  .modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
  
  .btn-secondary {
    padding: 8px 16px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    color: #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-secondary:hover {
    background: #4b5563;
  }
  
  .btn-primary {
    padding: 8px 16px;
    background: #3b82f6;
    border: 1px solid #3b82f6;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary:hover {
    background: #2563eb;
  }
  
  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
</style>