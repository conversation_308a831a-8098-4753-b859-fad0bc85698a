import * as vscode from 'vscode';
import { CommandManager } from './commands/commandManager';
import { WorkerThreadManager } from './services/workerThreadManager';
import { WebViewProvider } from './providers/webViewProvider';
import { SettingsManager } from './utils/settingsManager';
import { serviceRegistry } from './utils/serviceRegistry';

let commandManager: CommandManager;
let workerThreadManager: WorkerThreadManager;
let webViewProvider: WebViewProvider;
let settingsManager: SettingsManager;

export function activate(context: vscode.ExtensionContext): void {
  console.log('Metamorphic Reactor extension is now active');

  try {
    // Initialize core services
    settingsManager = new SettingsManager(context);
    commandManager = new CommandManager(context);
    workerThreadManager = new WorkerThreadManager(context);
    webViewProvider = new WebViewProvider(context);

    // Register services for cross-service communication
    serviceRegistry.register('webViewProvider', webViewProvider);
    serviceRegistry.register('workerThreadManager', workerThreadManager);
    serviceRegistry.register('settingsManager', settingsManager);
    serviceRegistry.register('commandManager', commandManager);

    // Register services
    commandManager.registerCommands();
    
    // Setup settings change listener
    const settingsDisposable = settingsManager.onSettingsChanged((settings) => {
      console.log('Settings changed:', settings);
      
      // Update worker thread manager with new settings
      if (workerThreadManager) {
        workerThreadManager.updateMaxWorkers(settings.maxAgents);
      }
      
      // Notify webview of settings change
      if (webViewProvider) {
        webViewProvider.sendMessage({
          command: 'settingsChanged',
          settings
        });
      }
    });

    // Add disposables to context
    context.subscriptions.push(
      commandManager,
      workerThreadManager,
      webViewProvider,
      settingsDisposable
    );

    console.log('Metamorphic Reactor initialized successfully');
  } catch (error) {
    console.error('Failed to activate Metamorphic Reactor:', error);
    vscode.window.showErrorMessage('Failed to activate Metamorphic Reactor extension');
  }
}

export async function deactivate(): Promise<void> {
  console.log('Metamorphic Reactor extension is being deactivated');

  try {
    // Graceful shutdown of worker threads first
    if (workerThreadManager) {
      await workerThreadManager.gracefulShutdown();
    }

    // Then cleanup other resources
    if (webViewProvider) {
      webViewProvider.dispose();
    }
    if (commandManager) {
      commandManager.dispose();
    }
    if (settingsManager) {
      // Settings manager doesn't need explicit disposal
    }

    // Clear service registry
    serviceRegistry.clear();

    console.log('Metamorphic Reactor deactivated successfully');
  } catch (error) {
    console.error('Error during deactivation:', error);
  }
}