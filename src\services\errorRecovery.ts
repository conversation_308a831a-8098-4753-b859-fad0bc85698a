import * as vscode from 'vscode';
import { WorkerThreadManager } from './workerThreadManager';
import { MessageProtocol } from './messageProtocol';

export interface ErrorRecoveryStrategy {
  name: string;
  canHandle: (error: Error, agentId: string) => boolean;
  recover: (error: Error, agentId: string, manager: WorkerThreadManager) => Promise<boolean>;
}

export interface RecoveryMetrics {
  errorCount: number;
  recoveryAttempts: number;
  successfulRecoveries: number;
  failedRecoveries: number;
  lastErrorTime: number;
  errorTypes: Map<string, number>;
}

export class ErrorRecoveryManager {
  private strategies: ErrorRecoveryStrategy[] = [];
  private metrics: Map<string, RecoveryMetrics> = new Map();
  private maxRetries: number = 3;
  private cooldownPeriod: number = 30000; // 30 seconds

  constructor(private workerManager: WorkerThreadManager) {
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    this.strategies = [
      {
        name: 'WorkerCrash',
        canHandle: (error: Error) => error.message.includes('Worker thread terminated'),
        recover: this.recoverFromWorkerCrash.bind(this)
      },
      {
        name: 'MessageTimeout',
        canHandle: (error: Error) => error.message.includes('Message timeout'),
        recover: this.recoverFromTimeout.bind(this)
      },
      {
        name: 'UnresponsiveAgent',
        canHandle: (error: Error) => error.message.includes('health check failed'),
        recover: this.recoverFromUnresponsiveAgent.bind(this)
      },
      {
        name: 'MemoryLeak',
        canHandle: (error: Error) => error.message.includes('out of memory'),
        recover: this.recoverFromMemoryLeak.bind(this)
      },
      {
        name: 'AutoGenServiceDown',
        canHandle: (error: Error) => error.message.includes('AutoGen service not available'),
        recover: this.recoverFromServiceDown.bind(this)
      }
    ];
  }

  public async handleError(error: Error, agentId: string): Promise<boolean> {
    console.error(`Error in agent ${agentId}:`, error);

    // Update metrics
    this.updateErrorMetrics(agentId, error);

    // Check if we should attempt recovery
    if (!this.shouldAttemptRecovery(agentId)) {
      console.warn(`Skipping recovery for agent ${agentId} - too many recent failures`);
      return false;
    }

    // Find appropriate recovery strategy
    const strategy = this.strategies.find(s => s.canHandle(error, agentId));
    if (!strategy) {
      console.warn(`No recovery strategy found for error in agent ${agentId}:`, error.message);
      return false;
    }

    console.log(`Attempting recovery for agent ${agentId} using strategy: ${strategy.name}`);
    
    try {
      const metrics = this.getMetrics(agentId);
      metrics.recoveryAttempts++;

      const success = await strategy.recover(error, agentId, this.workerManager);
      
      if (success) {
        metrics.successfulRecoveries++;
        console.log(`Successfully recovered agent ${agentId} using ${strategy.name}`);
      } else {
        metrics.failedRecoveries++;
        console.warn(`Recovery failed for agent ${agentId} using ${strategy.name}`);
      }

      return success;
    } catch (recoveryError) {
      console.error(`Recovery strategy ${strategy.name} threw error:`, recoveryError);
      this.getMetrics(agentId).failedRecoveries++;
      return false;
    }
  }

  private async recoverFromWorkerCrash(_error: Error, agentId: string, manager: WorkerThreadManager): Promise<boolean> {
    try {
      console.log(`Attempting to restart crashed worker: ${agentId}`);
      
      // Wait for cooldown period
      await this.delay(2000);
      
      // Terminate existing worker (if any)
      await manager.terminateWorkers();
      
      // Respawn the agent
      const taskId = `recovery-${Date.now()}`;
      await manager.spawnAgents(taskId);
      
      return true;
    } catch (err) {
      console.error(`Failed to recover from worker crash:`, err);
      return false;
    }
  }

  private async recoverFromTimeout(_error: Error, agentId: string, manager: WorkerThreadManager): Promise<boolean> {
    try {
      console.log(`Attempting to recover from timeout: ${agentId}`);
      
      // Send ping to check if agent is still responsive
      const pingMessage = MessageProtocol.createPingMessage(agentId);
      
      try {
        await manager.sendMessageToWorker(agentId, pingMessage, 3000);
        console.log(`Agent ${agentId} is responsive after timeout - no recovery needed`);
        return true;
      } catch (pingError) {
        console.log(`Agent ${agentId} is unresponsive - restarting worker`);
        return await this.recoverFromWorkerCrash(_error, agentId, manager);
      }
    } catch (err) {
      console.error(`Failed to recover from timeout:`, err);
      return false;
    }
  }

  private async recoverFromUnresponsiveAgent(_error: Error, agentId: string, manager: WorkerThreadManager): Promise<boolean> {
    try {
      console.log(`Attempting to recover unresponsive agent: ${agentId}`);
      
      // Try gentle restart first
      const stopMessage = MessageProtocol.createStopMessage(agentId);
      
      try {
        await manager.sendMessageToWorker(agentId, stopMessage, 5000);
        await this.delay(1000);
        
        // Check if agent recovered
        const pingMessage = MessageProtocol.createPingMessage(agentId);
        await manager.sendMessageToWorker(agentId, pingMessage, 3000);
        
        console.log(`Agent ${agentId} recovered after gentle restart`);
        return true;
      } catch (restartError) {
        console.log(`Gentle restart failed for ${agentId} - performing hard restart`);
        return await this.recoverFromWorkerCrash(_error, agentId, manager);
      }
    } catch (err) {
      console.error(`Failed to recover unresponsive agent:`, err);
      return false;
    }
  }

  private async recoverFromMemoryLeak(_error: Error, agentId: string, manager: WorkerThreadManager): Promise<boolean> {
    try {
      console.log(`Attempting to recover from memory leak: ${agentId}`);
      
      // Force garbage collection and restart
      if (global.gc) {
        global.gc();
      }
      
      // Restart the worker
      return await this.recoverFromWorkerCrash(_error, agentId, manager);
    } catch (err) {
      console.error(`Failed to recover from memory leak:`, err);
      return false;
    }
  }

  private async recoverFromServiceDown(_error: Error, agentId: string, _manager: WorkerThreadManager): Promise<boolean> {
    try {
      console.log(`Attempting to recover from AutoGen service down: ${agentId}`);
      
      // Switch to simulation mode for now
      vscode.window.showWarningMessage(
        'AutoGen service is unavailable. Running in simulation mode.',
        'Retry Connection'
      ).then(selection => {
        if (selection === 'Retry Connection') {
          // Attempt to reconnect to service
          this.retryServiceConnection(agentId, _manager);
        }
      });
      
      // Continue with simulation mode
      return true;
    } catch (err) {
      console.error(`Failed to recover from service down:`, err);
      return false;
    }
  }

  private async retryServiceConnection(agentId: string, _manager: WorkerThreadManager): Promise<void> {
    // Implementation would attempt to reconnect to AutoGen service
    console.log(`Retrying service connection for agent ${agentId}`);
  }

  private updateErrorMetrics(agentId: string, error: Error): void {
    const metrics = this.getMetrics(agentId);
    
    metrics.errorCount++;
    metrics.lastErrorTime = Date.now();
    
    const errorType = this.categorizeError(error);
    const count = metrics.errorTypes.get(errorType) || 0;
    metrics.errorTypes.set(errorType, count + 1);
  }

  private categorizeError(error: Error): string {
    const message = error.message.toLowerCase();
    
    if (message.includes('timeout')) return 'timeout';
    if (message.includes('memory')) return 'memory';
    if (message.includes('terminated')) return 'crash';
    if (message.includes('service')) return 'service';
    if (message.includes('network')) return 'network';
    
    return 'unknown';
  }

  private shouldAttemptRecovery(agentId: string): boolean {
    const metrics = this.getMetrics(agentId);
    const currentTime = Date.now();
    
    // Check if we're in cooldown period
    if (currentTime - metrics.lastErrorTime < this.cooldownPeriod) {
      // Allow recovery if this is the first error in the cooldown period
      if (metrics.errorCount === 1) return true;
      
      // Otherwise, check retry limit
      return metrics.recoveryAttempts < this.maxRetries;
    }
    
    // Reset metrics if cooldown period has passed
    if (currentTime - metrics.lastErrorTime > this.cooldownPeriod * 2) {
      this.resetMetrics(agentId);
    }
    
    return true;
  }

  private getMetrics(agentId: string): RecoveryMetrics {
    if (!this.metrics.has(agentId)) {
      this.metrics.set(agentId, {
        errorCount: 0,
        recoveryAttempts: 0,
        successfulRecoveries: 0,
        failedRecoveries: 0,
        lastErrorTime: 0,
        errorTypes: new Map()
      });
    }
    return this.metrics.get(agentId)!;
  }

  private resetMetrics(agentId: string): void {
    this.metrics.set(agentId, {
      errorCount: 0,
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      failedRecoveries: 0,
      lastErrorTime: 0,
      errorTypes: new Map()
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getRecoveryReport(): { [agentId: string]: RecoveryMetrics } {
    const report: { [agentId: string]: RecoveryMetrics } = {};
    
    for (const [agentId, metrics] of this.metrics) {
      report[agentId] = { ...metrics };
    }
    
    return report;
  }

  public dispose(): void {
    this.metrics.clear();
    this.strategies = [];
  }
}