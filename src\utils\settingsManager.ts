import * as vscode from 'vscode';

export interface MetamorphicSettings {
  maxAgents: number;
  apiKeys: {
    gemini: string[];
    claude: string[];
    openai: string[];
  };
  timeoutMinutes: number;
  maxRounds: number;
  requireConsensus: boolean;
  enableSecretScrubbing: boolean;
  enablePiiFiltering: boolean;
}

export class SettingsManager {
  private static readonly CONFIGURATION_KEY = 'metamorphic-reactor';

  constructor(_context: vscode.ExtensionContext) {
    // Context not used currently but may be needed for future features
  }

  public getSettings(): MetamorphicSettings {
    const config = vscode.workspace.getConfiguration(SettingsManager.CONFIGURATION_KEY);
    
    return {
      maxAgents: config.get<number>('maxAgents', 2),
      apiKeys: {
        gemini: config.get<string[]>('apiKeys.gemini', []),
        claude: config.get<string[]>('apiKeys.claude', []),
        openai: config.get<string[]>('apiKeys.openai', [])
      },
      timeoutMinutes: config.get<number>('timeoutMinutes', 15),
      maxRounds: config.get<number>('maxRounds', 10),
      requireConsensus: config.get<boolean>('requireConsensus', true),
      enableSecretScrubbing: config.get<boolean>('enableSecretScrubbing', true),
      enablePiiFiltering: config.get<boolean>('enablePiiFiltering', true)
    };
  }

  public async updateSettings(settings: Partial<MetamorphicSettings>): Promise<void> {
    const config = vscode.workspace.getConfiguration(SettingsManager.CONFIGURATION_KEY);
    
    const updatePromises: Thenable<void>[] = [];
    
    if (settings.maxAgents !== undefined) {
      updatePromises.push(config.update('maxAgents', settings.maxAgents, vscode.ConfigurationTarget.Global));
    }
    
    if (settings.apiKeys !== undefined) {
      if (settings.apiKeys.gemini !== undefined) {
        updatePromises.push(config.update('apiKeys.gemini', settings.apiKeys.gemini, vscode.ConfigurationTarget.Global));
      }
      if (settings.apiKeys.claude !== undefined) {
        updatePromises.push(config.update('apiKeys.claude', settings.apiKeys.claude, vscode.ConfigurationTarget.Global));
      }
      if (settings.apiKeys.openai !== undefined) {
        updatePromises.push(config.update('apiKeys.openai', settings.apiKeys.openai, vscode.ConfigurationTarget.Global));
      }
    }
    
    if (settings.timeoutMinutes !== undefined) {
      updatePromises.push(config.update('timeoutMinutes', settings.timeoutMinutes, vscode.ConfigurationTarget.Global));
    }
    
    if (settings.maxRounds !== undefined) {
      updatePromises.push(config.update('maxRounds', settings.maxRounds, vscode.ConfigurationTarget.Global));
    }
    
    if (settings.requireConsensus !== undefined) {
      updatePromises.push(config.update('requireConsensus', settings.requireConsensus, vscode.ConfigurationTarget.Global));
    }
    
    if (settings.enableSecretScrubbing !== undefined) {
      updatePromises.push(config.update('enableSecretScrubbing', settings.enableSecretScrubbing, vscode.ConfigurationTarget.Global));
    }
    
    if (settings.enablePiiFiltering !== undefined) {
      updatePromises.push(config.update('enablePiiFiltering', settings.enablePiiFiltering, vscode.ConfigurationTarget.Global));
    }

    await Promise.all(updatePromises);
  }

  public validateSettings(settings: MetamorphicSettings): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate maxAgents
    if (settings.maxAgents < 2 || settings.maxAgents > 5) {
      errors.push('Maximum agents must be between 2 and 5');
    }

    // Validate API keys
    const totalKeys = settings.apiKeys.gemini.length + settings.apiKeys.claude.length + settings.apiKeys.openai.length;
    if (totalKeys === 0) {
      errors.push('At least one API key must be configured');
    }

    // Validate API key formats (basic check)
    [...settings.apiKeys.gemini, ...settings.apiKeys.claude, ...settings.apiKeys.openai].forEach(key => {
      if (key.trim().length < 10) {
        errors.push('API keys must be at least 10 characters long');
      }
    });

    // Validate timeout
    if (settings.timeoutMinutes < 1 || settings.timeoutMinutes > 60) {
      errors.push('Timeout must be between 1 and 60 minutes');
    }

    // Validate max rounds
    if (settings.maxRounds < 1 || settings.maxRounds > 50) {
      errors.push('Maximum rounds must be between 1 and 50');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  public onSettingsChanged(callback: (settings: MetamorphicSettings) => void): vscode.Disposable {
    return vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration(SettingsManager.CONFIGURATION_KEY)) {
        callback(this.getSettings());
      }
    });
  }

  public getSubscriptionTier(): 'free' | 'pro' | 'dev' | 'enterprise' {
    const maxAgents = this.getSettings().maxAgents;
    
    switch (maxAgents) {
      case 2:
        return 'free';
      case 3:
        return 'pro';
      case 5:
        return 'dev';
      default:
        return 'enterprise';
    }
  }

  public getProviderKeys(provider: 'gemini' | 'claude' | 'openai'): string[] {
    const settings = this.getSettings();
    return settings.apiKeys[provider] || [];
  }

  public async addApiKey(provider: 'gemini' | 'claude' | 'openai', key: string): Promise<void> {
    const settings = this.getSettings();
    const existingKeys = settings.apiKeys[provider];
    
    if (!existingKeys.includes(key)) {
      const updatedKeys = [...existingKeys, key];
      await this.updateSettings({
        apiKeys: {
          ...settings.apiKeys,
          [provider]: updatedKeys
        }
      });
    }
  }

  public async removeApiKey(provider: 'gemini' | 'claude' | 'openai', key: string): Promise<void> {
    const settings = this.getSettings();
    const existingKeys = settings.apiKeys[provider];
    
    const updatedKeys = existingKeys.filter(k => k !== key);
    await this.updateSettings({
      apiKeys: {
        ...settings.apiKeys,
        [provider]: updatedKeys
      }
    });
  }
}