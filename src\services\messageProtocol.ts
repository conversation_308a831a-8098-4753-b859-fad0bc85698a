export interface WorkerMessage {
  type: 'task' | 'stop' | 'status' | 'ping' | 'health-check';
  payload?: any;
  messageId: string;
  timestamp: number;
  agentId: string | undefined;
}

export interface WorkerResponse {
  type: 'result' | 'error' | 'status' | 'token' | 'pong' | 'health';
  payload?: any;
  messageId: string | undefined;
  agentId: string;
  timestamp: number;
}

export interface TaskPayload {
  taskDescription: string;
  contextFiles: string[];
  maxRounds: number;
  timeoutMinutes: number;
  requireConsensus: boolean;
}

export interface AgentStatus {
  agentId: string;
  role: 'planner' | 'critic' | 'additional';
  status: 'idle' | 'running' | 'error' | 'stopped';
  lastActivity: number;
  currentTask?: string;
}

export interface TaskResult {
  agentId: string;
  role: string;
  response: string;
  consensus: boolean;
  timestamp: string;
  tokenCount?: number;
}

export class MessageProtocol {
  private static messageIdCounter = 0;

  public static generateMessageId(): string {
    return `msg_${Date.now()}_${++this.messageIdCounter}`;
  }

  public static createTaskMessage(taskPayload: TaskPayload, agentId?: string): WorkerMessage {
    return {
      type: 'task',
      payload: taskPayload,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      agentId
    };
  }

  public static createStopMessage(agentId?: string): WorkerMessage {
    return {
      type: 'stop',
      payload: null,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      agentId
    };
  }

  public static createStatusMessage(agentId?: string): WorkerMessage {
    return {
      type: 'status',
      payload: null,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      agentId
    };
  }

  public static createPingMessage(agentId?: string): WorkerMessage {
    return {
      type: 'ping',
      payload: null,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      agentId
    };
  }

  public static createHealthCheckMessage(): WorkerMessage {
    return {
      type: 'health-check',
      payload: null,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      agentId: undefined
    };
  }

  public static isValidMessage(message: any): message is WorkerMessage {
    return (
      message &&
      typeof message === 'object' &&
      typeof message.type === 'string' &&
      typeof message.messageId === 'string' &&
      typeof message.timestamp === 'number'
    );
  }

  public static isValidResponse(response: any): response is WorkerResponse {
    return (
      response &&
      typeof response === 'object' &&
      typeof response.type === 'string' &&
      typeof response.agentId === 'string' &&
      typeof response.timestamp === 'number'
    );
  }

  public static isTaskComplete(responses: TaskResult[]): boolean {
    if (responses.length === 0) return false;
    
    // Check if all agents have responded
    const roles = new Set(responses.map(r => r.role));
    const hasPlanner = roles.has('planner');
    const hasCritic = roles.has('critic');
    
    if (!hasPlanner || !hasCritic) return false;
    
    // Check consensus (all agents must agree)
    return responses.every(r => r.consensus === true);
  }

  public static calculateConsensusScore(responses: TaskResult[]): number {
    if (responses.length === 0) return 0;
    
    const consensusCount = responses.filter(r => r.consensus).length;
    return consensusCount / responses.length;
  }

  public static detectMetaBlock(responses: TaskResult[], maxRounds: number, currentRound: number): boolean {
    // Meta-block conditions:
    // 1. Reached maximum rounds without consensus
    // 2. Conflicting responses with no progress
    // 3. Agents repeatedly changing their stance
    
    if (currentRound >= maxRounds) {
      return true;
    }
    
    const consensusScore = this.calculateConsensusScore(responses);
    if (consensusScore < 0.5 && currentRound > 3) {
      return true;
    }
    
    return false;
  }

  public static serializeMessage(message: WorkerMessage): string {
    try {
      return JSON.stringify(message);
    } catch (error) {
      throw new Error(`Failed to serialize message: ${error}`);
    }
  }

  public static deserializeMessage(data: string): WorkerMessage {
    try {
      const message = JSON.parse(data);
      if (!this.isValidMessage(message)) {
        throw new Error('Invalid message format');
      }
      return message;
    } catch (error) {
      throw new Error(`Failed to deserialize message: ${error}`);
    }
  }

  public static serializeResponse(response: WorkerResponse): string {
    try {
      return JSON.stringify(response);
    } catch (error) {
      throw new Error(`Failed to serialize response: ${error}`);
    }
  }

  public static deserializeResponse(data: string): WorkerResponse {
    try {
      const response = JSON.parse(data);
      if (!this.isValidResponse(response)) {
        throw new Error('Invalid response format');
      }
      return response;
    } catch (error) {
      throw new Error(`Failed to deserialize response: ${error}`);
    }
  }
}