{"version": "2.0.0", "tasks": [{"label": "npm: compile", "type": "shell", "command": "npm", "args": ["run", "compile"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$tsc"}, {"label": "npm: watch", "type": "shell", "command": "npm", "args": ["run", "watch"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$tsc-watch", "isBackground": true}, {"label": "npm: lint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "test"}, {"label": "npm: test", "type": "shell", "command": "npm", "args": ["test"], "group": "test"}, {"label": "python: install", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}/python"}, "group": "build"}, {"label": "python: start", "type": "shell", "command": "python", "args": ["main.py"], "options": {"cwd": "${workspaceFolder}/python"}, "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "^.*Application startup complete.*$"}}}, {"label": "python: test", "type": "shell", "command": "pytest", "options": {"cwd": "${workspaceFolder}/python"}, "group": "test"}, {"label": "webview: build", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/webview"}, "group": "build"}, {"label": "webview: dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/webview"}, "group": "build", "isBackground": true}]}