<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  export let agentData: AgentConfig;
  export let showInline: boolean = true;
  export let showSummary: boolean = true;
  export let autoValidate: boolean = true;
  export let validationMode: 'strict' | 'relaxed' | 'custom' = 'strict';
  
  interface AgentConfig {
    name: string;
    role: string;
    model: string;
    prompt: string;
    capabilities: string[];
    team?: string;
    metadata?: {
      description?: string;
      tags?: string[];
    };
  }
  
  interface ValidationRule {
    field: string;
    type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
    value?: any;
    message: string;
    severity: 'error' | 'warning' | 'info';
  }
  
  interface ValidationResult {
    field: string;
    isValid: boolean;
    message: string;
    severity: 'error' | 'warning' | 'info';
    suggestions?: string[];
  }
  
  // Validation rules based on mode
  const validationRules: Record<string, ValidationRule[]> = {
    strict: [
      { field: 'name', type: 'required', message: 'Agent name is required', severity: 'error' },
      { field: 'name', type: 'minLength', value: 3, message: 'Agent name must be at least 3 characters', severity: 'error' },
      { field: 'name', type: 'maxLength', value: 50, message: 'Agent name must be less than 50 characters', severity: 'error' },
      { field: 'name', type: 'pattern', value: /^[a-zA-Z0-9\\s\\-_]+$/, message: 'Agent name contains invalid characters', severity: 'error' },
      
      { field: 'role', type: 'required', message: 'Role selection is required', severity: 'error' },
      
      { field: 'model', type: 'required', message: 'Model selection is required', severity: 'error' },
      
      { field: 'prompt', type: 'required', message: 'Prompt is required', severity: 'error' },
      { field: 'prompt', type: 'minLength', value: 20, message: 'Prompt should be at least 20 characters for effective operation', severity: 'warning' },
      { field: 'prompt', type: 'maxLength', value: 8000, message: 'Prompt is too long (max 8000 characters)', severity: 'error' },
      
      { field: 'capabilities', type: 'custom', message: 'At least 3 capabilities are recommended', severity: 'warning' },
      { field: 'capabilities', type: 'custom', message: 'Too many capabilities may dilute agent focus', severity: 'warning' }
    ],
    relaxed: [
      { field: 'name', type: 'required', message: 'Agent name is required', severity: 'error' },
      { field: 'name', type: 'minLength', value: 1, message: 'Agent name cannot be empty', severity: 'error' },
      { field: 'role', type: 'required', message: 'Role selection is required', severity: 'warning' },
      { field: 'model', type: 'required', message: 'Model selection is required', severity: 'warning' },
      { field: 'prompt', type: 'minLength', value: 10, message: 'Consider adding a more detailed prompt', severity: 'info' }
    ],
    custom: []
  };
  
  // Real-time validation results
  let validationResults: ValidationResult[] = [];
  let overallScore: number = 0;
  let isFormValid: boolean = false;
  
  // Validation suggestions
  const suggestions = {
    name: [
      'Use descriptive names like "Code Review Assistant" or "Data Analysis Expert"',
      'Avoid generic names like "Agent1" or "Helper"',
      'Consider including the agent\'s primary function in the name'
    ],
    role: [
      'Choose the role that best matches your intended use case',
      'Coder: For programming and development tasks',
      'Analyst: For data analysis and reporting',
      'Writer: For content creation and documentation'
    ],
    model: [
      'GPT-4: Best for complex reasoning and code generation',
      'Claude-3: Excellent for analysis and writing',
      'Consider cost vs performance for your use case'
    ],
    prompt: [
      'Be specific about the agent\'s behavior and constraints',
      'Include examples of desired inputs and outputs',
      'Define the agent\'s expertise and limitations',
      'Use clear, unambiguous language'
    ],
    capabilities: [
      'Add capabilities that match your agent\'s role',
      'Include both technical skills and domain knowledge',
      '3-8 capabilities provide good focus without dilution'
    ]
  };
  
  // Advanced validation patterns
  const promptPatterns = {
    hasVariables: /\{[^}]+\}/g,
    hasCommands: /\[[A-Z_]+\]/g,
    hasStructure: /(guidelines|rules|steps|approach|format)/i,
    hasExamples: /(example|instance|sample|demonstration)/i,
    hasConstraints: /(don't|avoid|never|must not|should not)/i
  };
  
  // Role-specific validation
  const roleRequirements = {
    coder: {
      requiredCapabilities: ['programming', 'debugging', 'code_review'],
      recommendedPromptElements: ['coding standards', 'testing', 'documentation'],
      minPromptLength: 50
    },
    debugger: {
      requiredCapabilities: ['debugging', 'analysis', 'problem_solving'],
      recommendedPromptElements: ['error analysis', 'troubleshooting', 'testing'],
      minPromptLength: 40
    },
    pm: {
      requiredCapabilities: ['project_management', 'planning', 'communication'],
      recommendedPromptElements: ['methodology', 'stakeholders', 'deliverables'],
      minPromptLength: 60
    },
    analyst: {
      requiredCapabilities: ['data_analysis', 'statistics', 'reporting'],
      recommendedPromptElements: ['methodology', 'metrics', 'insights'],
      minPromptLength: 50
    }
  };
  
  $: if (autoValidate) {
    validateForm();
  }
  
  function validateForm() {
    const rules = validationRules[validationMode] || [];
    const results: ValidationResult[] = [];
    
    // Validate each rule
    rules.forEach(rule => {
      const result = validateField(rule);
      if (result) {
        results.push(result);
      }
    });
    
    // Add role-specific validation
    const roleValidation = validateRoleSpecific();
    results.push(...roleValidation);
    
    // Add advanced prompt validation
    const promptValidation = validatePromptAdvanced();
    results.push(...promptValidation);
    
    // Add capability validation
    const capabilityValidation = validateCapabilities();
    results.push(...capabilityValidation);
    
    validationResults = results;
    overallScore = calculateOverallScore();
    isFormValid = !results.some(r => r.severity === 'error');
    
    dispatch('validation', {
      results: validationResults,
      score: overallScore,
      isValid: isFormValid
    });
  }
  
  function validateField(rule: ValidationRule): ValidationResult | null {
    const value = getFieldValue(rule.field);
    let isValid = true;
    let message = rule.message;
    const suggestions: string[] = [];
    
    switch (rule.type) {
      case 'required':
        isValid = value !== undefined && value !== null && value !== '';
        if (!isValid && rule.field in suggestions) {
          suggestions.push(...suggestions[rule.field]);
        }
        break;
        
      case 'minLength':
        isValid = value && value.length >= rule.value;
        if (!isValid) {
          message = `${rule.field} must be at least ${rule.value} characters (current: ${value?.length || 0})`;
        }
        break;
        
      case 'maxLength':
        isValid = !value || value.length <= rule.value;
        if (!isValid) {
          message = `${rule.field} exceeds maximum length of ${rule.value} characters (current: ${value.length})`;
        }
        break;
        
      case 'pattern':
        isValid = !value || rule.value.test(value);
        break;
        
      case 'custom':
        if (rule.field === 'capabilities') {
          if (rule.message.includes('At least 3')) {
            isValid = (agentData.capabilities?.length || 0) >= 3;
            if (!isValid) {
              suggestions.push('Add more capabilities to improve agent specialization');
            }
          } else if (rule.message.includes('Too many')) {
            isValid = (agentData.capabilities?.length || 0) <= 12;
            if (!isValid) {
              suggestions.push('Consider focusing on fewer, more specific capabilities');
            }
          }
        }
        break;
    }
    
    if (!isValid || (rule.severity === 'warning' && !isValid)) {
      return {
        field: rule.field,
        isValid,
        message,
        severity: rule.severity,
        suggestions: suggestions.length > 0 ? suggestions : undefined
      };
    }
    
    return null;
  }
  
  function validateRoleSpecific(): ValidationResult[] {
    const results: ValidationResult[] = [];
    
    if (!agentData.role) return results;
    
    const requirements = roleRequirements[agentData.role.toLowerCase()];
    if (!requirements) return results;
    
    // Check required capabilities
    const missingCapabilities = requirements.requiredCapabilities.filter(cap => 
      !agentData.capabilities?.some(agentCap => 
        agentCap.toLowerCase().includes(cap.toLowerCase())
      )
    );
    
    if (missingCapabilities.length > 0) {
      results.push({
        field: 'capabilities',
        isValid: false,
        message: `Missing role-specific capabilities: ${missingCapabilities.join(', ')}`,
        severity: 'warning',
        suggestions: [`Add capabilities: ${missingCapabilities.join(', ')}`]
      });
    }
    
    // Check prompt elements
    const prompt = agentData.prompt?.toLowerCase() || '';
    const missingElements = requirements.recommendedPromptElements.filter(element => 
      !prompt.includes(element.toLowerCase())
    );
    
    if (missingElements.length > 0) {
      results.push({
        field: 'prompt',
        isValid: true,
        message: `Consider including: ${missingElements.join(', ')}`,
        severity: 'info',
        suggestions: [`Add guidance about: ${missingElements.join(', ')}`]
      });
    }
    
    // Check minimum prompt length for role
    if (agentData.prompt && agentData.prompt.length < requirements.minPromptLength) {
      results.push({
        field: 'prompt',
        isValid: false,
        message: `Prompt too brief for ${agentData.role} role (minimum ${requirements.minPromptLength} characters)`,
        severity: 'warning',
        suggestions: ['Add more detailed instructions for this role']
      });
    }
    
    return results;
  }
  
  function validatePromptAdvanced(): ValidationResult[] {
    const results: ValidationResult[] = [];
    const prompt = agentData.prompt || '';
    
    if (!prompt) return results;
    
    // Check for structure
    const hasStructure = promptPatterns.hasStructure.test(prompt);
    if (!hasStructure) {
      results.push({
        field: 'prompt',
        isValid: true,
        message: 'Consider adding structure (guidelines, steps, or approach)',
        severity: 'info',
        suggestions: ['Add clear guidelines or step-by-step approach']
      });
    }
    
    // Check for examples
    const hasExamples = promptPatterns.hasExamples.test(prompt);
    if (!hasExamples && prompt.length > 100) {
      results.push({
        field: 'prompt',
        isValid: true,
        message: 'Adding examples can improve agent performance',
        severity: 'info',
        suggestions: ['Include examples of desired inputs and outputs']
      });
    }
    
    // Check for constraints
    const hasConstraints = promptPatterns.hasConstraints.test(prompt);
    if (!hasConstraints && validationMode === 'strict') {
      results.push({
        field: 'prompt',
        isValid: true,
        message: 'Consider adding constraints or limitations',
        severity: 'info',
        suggestions: ['Specify what the agent should avoid or not do']
      });
    }
    
    // Check for variables
    const variables = prompt.match(promptPatterns.hasVariables);
    if (variables && variables.length > 5) {
      results.push({
        field: 'prompt',
        isValid: true,
        message: 'Many variables detected - ensure they are all necessary',
        severity: 'warning',
        suggestions: ['Review variable usage for clarity']
      });
    }
    
    return results;
  }
  
  function validateCapabilities(): ValidationResult[] {
    const results: ValidationResult[] = [];
    const capabilities = agentData.capabilities || [];
    
    if (capabilities.length === 0) {
      results.push({
        field: 'capabilities',
        isValid: false,
        message: 'No capabilities selected',
        severity: 'warning',
        suggestions: ['Add capabilities that match your agent\'s intended purpose']
      });
      return results;
    }
    
    // Check for capability diversity
    const categories = {
      technical: ['programming', 'debugging', 'testing', 'development', 'coding'],
      analytical: ['analysis', 'data', 'statistics', 'research', 'insights'],
      communication: ['writing', 'documentation', 'presentation', 'reporting'],
      management: ['planning', 'coordination', 'leadership', 'organization']
    };
    
    const categoryCount = Object.keys(categories).filter(category => 
      capabilities.some(cap => 
        categories[category].some(keyword => 
          cap.toLowerCase().includes(keyword)
        )
      )
    ).length;
    
    if (categoryCount === 1 && capabilities.length > 3) {
      results.push({
        field: 'capabilities',
        isValid: true,
        message: 'Capabilities are focused in one area - consider adding diversity',
        severity: 'info',
        suggestions: ['Add capabilities from different domains for versatility']
      });
    }
    
    // Check for contradictory capabilities
    const contradictions = [
      { set1: ['beginner', 'basic'], set2: ['expert', 'advanced'], message: 'Conflicting skill levels detected' },
      { set1: ['frontend'], set2: ['backend'], message: 'Full-stack capabilities detected - ensure this is intended' }
    ];
    
    contradictions.forEach(contradiction => {
      const hasSet1 = capabilities.some(cap => 
        contradiction.set1.some(keyword => cap.toLowerCase().includes(keyword))
      );
      const hasSet2 = capabilities.some(cap => 
        contradiction.set2.some(keyword => cap.toLowerCase().includes(keyword))
      );
      
      if (hasSet1 && hasSet2) {
        results.push({
          field: 'capabilities',
          isValid: true,
          message: contradiction.message,
          severity: 'warning',
          suggestions: ['Review capabilities for consistency']
        });
      }
    });
    
    return results;
  }
  
  function getFieldValue(field: string): any {
    switch (field) {
      case 'name': return agentData.name;
      case 'role': return agentData.role;
      case 'model': return agentData.model;
      case 'prompt': return agentData.prompt;
      case 'capabilities': return agentData.capabilities;
      case 'team': return agentData.team;
      default: return null;
    }
  }
  
  function calculateOverallScore(): number {
    const totalPossible = 100;
    let score = totalPossible;
    
    validationResults.forEach(result => {
      if (!result.isValid) {
        switch (result.severity) {
          case 'error': score -= 20; break;
          case 'warning': score -= 10; break;
          case 'info': score -= 2; break;
        }
      }
    });
    
    // Bonus points for advanced features
    if (agentData.prompt?.length > 200) score += 5;
    if ((agentData.capabilities?.length || 0) >= 5) score += 5;
    if (agentData.metadata?.description) score += 3;
    if (agentData.team) score += 2;
    
    return Math.max(0, Math.min(100, score));
  }
  
  function getSeverityIcon(severity: string): string {
    switch (severity) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '';
    }
  }
  
  function getSeverityColor(severity: string): string {
    switch (severity) {
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'info': return '#3b82f6';
      default: return '#6b7280';
    }
  }
  
  function getScoreColor(score: number): string {
    if (score >= 80) return '#10b981';
    if (score >= 60) return '#f59e0b';
    return '#ef4444';
  }
  
  function getScoreLabel(score: number): string {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 40) return 'Needs Improvement';
    return 'Poor';
  }
  
  // Export validation function for external use
  export function validate() {
    validateForm();
    return {
      results: validationResults,
      score: overallScore,
      isValid: isFormValid
    };
  }
  
  // Focus on first error field
  export function focusFirstError() {
    const firstError = validationResults.find(r => r.severity === 'error');
    if (firstError) {
      dispatch('focusField', { field: firstError.field });
    }
  }
</script>

<div class="agent-form-validator">
  {#if showSummary}
    <div class="validation-summary">
      <div class="score-display">
        <div class="score-circle" style="border-color: {getScoreColor(overallScore)}">
          <span class="score-value" style="color: {getScoreColor(overallScore)}">{overallScore}</span>
          <span class="score-max">/100</span>
        </div>
        <div class="score-info">
          <span class="score-label" style="color: {getScoreColor(overallScore)}">{getScoreLabel(overallScore)}</span>
          <span class="score-description">Agent Configuration Quality</span>
        </div>
      </div>
      
      <div class="validation-stats">
        <div class="stat-item">
          <span class="stat-value">{validationResults.filter(r => r.severity === 'error').length}</span>
          <span class="stat-label">Errors</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{validationResults.filter(r => r.severity === 'warning').length}</span>
          <span class="stat-label">Warnings</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{validationResults.filter(r => r.severity === 'info').length}</span>
          <span class="stat-label">Suggestions</span>
        </div>
      </div>
    </div>
  {/if}
  
  {#if showInline && validationResults.length > 0}
    <div class="validation-messages">
      <h4>Validation Results</h4>
      <div class="messages-list">
        {#each validationResults as result}
          <div class="validation-message {result.severity}" style="border-left-color: {getSeverityColor(result.severity)}">
            <div class="message-header">
              <span class="message-icon">{getSeverityIcon(result.severity)}</span>
              <span class="message-field">{result.field.charAt(0).toUpperCase() + result.field.slice(1)}</span>
              <span class="message-text">{result.message}</span>
            </div>
            
            {#if result.suggestions && result.suggestions.length > 0}
              <div class="message-suggestions">
                <strong>Suggestions:</strong>
                <ul>
                  {#each result.suggestions as suggestion}
                    <li>{suggestion}</li>
                  {/each}
                </ul>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    </div>
  {/if}
  
  <!-- Quick Fix Suggestions -->
  {#if validationResults.some(r => r.severity === 'error' || r.severity === 'warning')}
    <div class="quick-fixes">
      <h4>Quick Fixes</h4>
      <div class="fixes-list">
        {#if !agentData.name}
          <button class="fix-btn" on:click={() => dispatch('quickFix', { field: 'name', action: 'focus' })}>
            📝 Add Agent Name
          </button>
        {/if}
        
        {#if !agentData.role}
          <button class="fix-btn" on:click={() => dispatch('quickFix', { field: 'role', action: 'suggest' })}>
            👤 Select Role
          </button>
        {/if}
        
        {#if !agentData.model}
          <button class="fix-btn" on:click={() => dispatch('quickFix', { field: 'model', action: 'suggest' })}>
            🤖 Choose Model
          </button>
        {/if}
        
        {#if !agentData.prompt || agentData.prompt.length < 20}
          <button class="fix-btn" on:click={() => dispatch('quickFix', { field: 'prompt', action: 'template' })}>
            ✍️ Improve Prompt
          </button>
        {/if}
        
        {#if (agentData.capabilities?.length || 0) < 3}
          <button class="fix-btn" on:click={() => dispatch('quickFix', { field: 'capabilities', action: 'suggest' })}>
            ⚡ Add Capabilities
          </button>
        {/if}
      </div>
    </div>
  {/if}
  
  <!-- Field-specific tips -->
  <div class="validation-tips">
    <h4>Validation Tips</h4>
    <div class="tips-grid">
      <div class="tip-card">
        <h5>📝 Agent Name</h5>
        <ul>
          <li>Use descriptive, unique names</li>
          <li>Include the agent's primary function</li>
          <li>Avoid generic names like "Agent1"</li>
        </ul>
      </div>
      
      <div class="tip-card">
        <h5>🎭 Role Selection</h5>
        <ul>
          <li>Choose based on primary use case</li>
          <li>Role affects available templates</li>
          <li>Consider hybrid roles for complex tasks</li>
        </ul>
      </div>
      
      <div class="tip-card">
        <h5>✍️ Prompt Writing</h5>
        <ul>
          <li>Be specific and unambiguous</li>
          <li>Include examples when helpful</li>
          <li>Define constraints and limitations</li>
        </ul>
      </div>
      
      <div class="tip-card">
        <h5>⚡ Capabilities</h5>
        <ul>
          <li>Match capabilities to role</li>
          <li>Balance breadth and depth</li>
          <li>Include both skills and knowledge</li>
        </ul>
      </div>
    </div>
  </div>
</div>

<style>
  .agent-form-validator {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .validation-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #111827;
    border-radius: 8px;
  }
  
  .score-display {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .score-circle {
    width: 80px;
    height: 80px;
    border: 4px solid;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  
  .score-value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
  }
  
  .score-max {
    font-size: 12px;
    color: #9ca3af;
    line-height: 1;
  }
  
  .score-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .score-label {
    font-size: 18px;
    font-weight: 600;
  }
  
  .score-description {
    font-size: 14px;
    color: #9ca3af;
  }
  
  .validation-stats {
    display: flex;
    gap: 24px;
  }
  
  .stat-item {
    text-align: center;
  }
  
  .stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
  }
  
  .stat-label {
    display: block;
    font-size: 12px;
    color: #9ca3af;
    margin-top: 2px;
  }
  
  .validation-messages {
    margin-bottom: 20px;
  }
  
  .validation-messages h4 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .messages-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .validation-message {
    padding: 12px;
    background: #111827;
    border: 1px solid #374151;
    border-left: 4px solid;
    border-radius: 6px;
  }
  
  .validation-message.error {
    background: rgba(239, 68, 68, 0.05);
    border-color: #374151;
  }
  
  .validation-message.warning {
    background: rgba(245, 158, 11, 0.05);
    border-color: #374151;
  }
  
  .validation-message.info {
    background: rgba(59, 130, 246, 0.05);
    border-color: #374151;
  }
  
  .message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }
  
  .message-icon {
    font-size: 14px;
    flex-shrink: 0;
  }
  
  .message-field {
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
  }
  
  .message-text {
    color: #d1d5db;
    font-size: 14px;
  }
  
  .message-suggestions {
    margin-top: 8px;
    font-size: 13px;
    color: #9ca3af;
  }
  
  .message-suggestions strong {
    color: #d1d5db;
  }
  
  .message-suggestions ul {
    margin: 4px 0 0 0;
    padding-left: 16px;
  }
  
  .message-suggestions li {
    margin-bottom: 2px;
  }
  
  .quick-fixes {
    margin-bottom: 20px;
  }
  
  .quick-fixes h4 {
    margin: 0 0 12px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .fixes-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .fix-btn {
    padding: 6px 12px;
    background: #3b82f6;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .fix-btn:hover {
    background: #2563eb;
  }
  
  .validation-tips h4 {
    margin: 0 0 16px 0;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
  }
  
  .tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .tip-card {
    background: #111827;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 16px;
  }
  
  .tip-card h5 {
    margin: 0 0 8px 0;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
  }
  
  .tip-card ul {
    margin: 0;
    padding-left: 16px;
  }
  
  .tip-card li {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 4px;
  }
</style>