"""
Metamorphic Reactor - Python AutoGen Microservice
Main FastAPI application for multi-agent orchestration
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
import logging
from typing import Dict, Any

from api.routes import task_router, agent_router, health_router, set_orchestrator
from api.admin_routes import admin_router
from agents.orchestrator import AgentOrchestrator
from utils.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global orchestrator instance
orchestrator: AgentOrchestrator = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global orchestrator
    
    # Startup
    logger.info("Starting Metamorphic Reactor microservice")
    orchestrator = AgentOrchestrator()
    await orchestrator.initialize()
    app.state.orchestrator = orchestrator
    
    # Set orchestrator reference in routes
    set_orchestrator(orchestrator)
    
    yield
    
    # Shutdown
    logger.info("Shutting down Metamorphic Reactor microservice")
    if orchestrator:
        await orchestrator.cleanup()


app = FastAPI(
    title="Metamorphic Reactor",
    description="Multi-Agent LLM Orchestration with Autonomous Consensus",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware for local development
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "vscode-webview://*",  # VS Code WebView origin
        "http://localhost:*",   # Local development
        "http://127.0.0.1:*",   # Local development
        "https://localhost:*",  # HTTPS local development
        "*"  # Fallback for development - restrict in production
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Content-Type", 
        "Authorization", 
        "Accept", 
        "Origin", 
        "X-Requested-With",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers"
    ],
    expose_headers=["*"]
)

# Include routers
app.include_router(health_router, prefix="/health", tags=["health"])
app.include_router(task_router, prefix="/tasks", tags=["tasks"])
app.include_router(agent_router, prefix="/agents", tags=["agents"])
app.include_router(admin_router, prefix="/admin", tags=["admin"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Metamorphic Reactor API",
        "version": "1.0.0",
        "status": "running"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )